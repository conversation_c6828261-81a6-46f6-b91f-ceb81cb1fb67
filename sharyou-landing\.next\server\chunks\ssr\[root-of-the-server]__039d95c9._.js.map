{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Sharyou/sharyou-landing/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState } from 'react';\nimport { ChevronDownIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\n\ninterface HeaderProps {\n  language: 'fr' | 'ar';\n  setLanguage: (lang: 'fr' | 'ar') => void;\n}\n\nconst translations = {\n  fr: {\n    features: 'Fonctionnalités',\n    pricing: 'Tarifs',\n    resources: 'Ressources',\n    support: 'Support',\n    login: 'Connexion',\n    signup: 'Commencer gratuitement'\n  },\n  ar: {\n    features: 'الميزات',\n    pricing: 'الأسعار',\n    resources: 'الموارد',\n    support: 'الدعم',\n    login: 'تسجيل الدخول',\n    signup: 'ابدأ مجاناً'\n  }\n};\n\nexport default function Header({ language, setLanguage }: HeaderProps) {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isLangDropdownOpen, setIsLangDropdownOpen] = useState(false);\n  const t = translations[language];\n\n  return (\n    <motion.header\n      initial={{ y: -100, opacity: 0 }}\n      animate={{ y: 0, opacity: 1 }}\n      transition={{ duration: 0.6, ease: \"easeOut\" }}\n      className=\"fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-100\"\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex items-center\"\n          >\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">S</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">Sharyou</span>\n            </div>\n          </motion.div>\n\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <a href=\"#features\" className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\">\n              {t.features}\n            </a>\n            <a href=\"#pricing\" className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\">\n              {t.pricing}\n            </a>\n            <a href=\"#resources\" className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\">\n              {t.resources}\n            </a>\n            <a href=\"#support\" className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\">\n              {t.support}\n            </a>\n          </nav>\n\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <div className=\"relative\">\n              <button\n                onClick={() => setIsLangDropdownOpen(!isLangDropdownOpen)}\n                className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors\"\n              >\n                <span className=\"text-sm font-medium\">\n                  {language === 'fr' ? 'FR' : 'العربية'}\n                </span>\n                <ChevronDownIcon className=\"w-4 h-4\" />\n              </button>\n              \n              {isLangDropdownOpen && (\n                <motion.div\n                  initial={{ opacity: 0, y: -10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: -10 }}\n                  className=\"absolute top-full mt-2 right-0 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-[120px]\"\n                >\n                  <button\n                    onClick={() => {\n                      setLanguage('fr');\n                      setIsLangDropdownOpen(false);\n                    }}\n                    className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                  >\n                    Français\n                  </button>\n                  <button\n                    onClick={() => {\n                      setLanguage('ar');\n                      setIsLangDropdownOpen(false);\n                    }}\n                    className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50\"\n                  >\n                    العربية\n                  </button>\n                </motion.div>\n              )}\n            </div>\n\n            <a href=\"#login\" className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\">\n              {t.login}\n            </a>\n            \n            <motion.a\n              href=\"#signup\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"btn-primary\"\n            >\n              {t.signup}\n            </motion.a>\n          </div>\n\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"w-6 h-6\" />\n              ) : (\n                <Bars3Icon className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {isMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"md:hidden border-t border-gray-100 py-4\"\n          >\n            <div className=\"flex flex-col space-y-4\">\n              <a href=\"#features\" className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\">\n                {t.features}\n              </a>\n              <a href=\"#pricing\" className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\">\n                {t.pricing}\n              </a>\n              <a href=\"#resources\" className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\">\n                {t.resources}\n              </a>\n              <a href=\"#support\" className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\">\n                {t.support}\n              </a>\n              <div className=\"flex items-center justify-between pt-4 border-t border-gray-100\">\n                <div className=\"flex space-x-2\">\n                  <button\n                    onClick={() => setLanguage('fr')}\n                    className={`px-3 py-1 rounded text-sm ${language === 'fr' ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}`}\n                  >\n                    FR\n                  </button>\n                  <button\n                    onClick={() => setLanguage('ar')}\n                    className={`px-3 py-1 rounded text-sm ${language === 'ar' ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}`}\n                  >\n                    AR\n                  </button>\n                </div>\n                <div className=\"flex space-x-2\">\n                  <a href=\"#login\" className=\"text-gray-700 hover:text-blue-600 transition-colors font-medium\">\n                    {t.login}\n                  </a>\n                  <a href=\"#signup\" className=\"btn-primary text-sm\">\n                    {t.signup}\n                  </a>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </div>\n    </motion.header>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAGA;;;;;;AAHA;;;;;AAWA,MAAM,eAAe;IACnB,IAAI;QACF,UAAU;QACV,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IACA,IAAI;QACF,UAAU;QACV,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;IACV;AACF;AAEe,SAAS,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAe;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,IAAI,YAAY,CAAC,SAAS;IAEhC,qBACE,8OAAC,OAAO,MAAM;QACZ,SAAS;YAAE,GAAG,CAAC;YAAK,SAAS;QAAE;QAC/B,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,OAAO,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;sCAItD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAY,WAAU;8CAC3B,EAAE,QAAQ;;;;;;8CAEb,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAC1B,EAAE,OAAO;;;;;;8CAEZ,8OAAC;oCAAE,MAAK;oCAAa,WAAU;8CAC5B,EAAE,SAAS;;;;;;8CAEd,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAC1B,EAAE,OAAO;;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,sBAAsB,CAAC;4CACtC,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DACb,aAAa,OAAO,OAAO;;;;;;8DAE9B,8OAAC;oDAAgB,WAAU;;;;;;;;;;;;wCAG5B,oCACC,8OAAC,OAAO,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,MAAM;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC3B,WAAU;;8DAEV,8OAAC;oDACC,SAAS;wDACP,YAAY;wDACZ,sBAAsB;oDACxB;oDACA,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS;wDACP,YAAY;wDACZ,sBAAsB;oDACxB;oDACA,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAOP,8OAAC;oCAAE,MAAK;oCAAS,WAAU;8CACxB,EAAE,KAAK;;;;;;8CAGV,8OAAC,OAAO,CAAC;oCACP,MAAK;oCACL,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CAET,EAAE,MAAM;;;;;;;;;;;;sCAIb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAET,2BACC,8OAAC;oCAAU,WAAU;;;;;yDAErB,8OAAC;oCAAU,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM5B,4BACC,8OAAC,OAAO,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,MAAK;gCAAY,WAAU;0CAC3B,EAAE,QAAQ;;;;;;0CAEb,8OAAC;gCAAE,MAAK;gCAAW,WAAU;0CAC1B,EAAE,OAAO;;;;;;0CAEZ,8OAAC;gCAAE,MAAK;gCAAa,WAAU;0CAC5B,EAAE,SAAS;;;;;;0CAEd,8OAAC;gCAAE,MAAK;gCAAW,WAAU;0CAC1B,EAAE,OAAO;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,0BAA0B,EAAE,aAAa,OAAO,8BAA8B,iBAAiB;0DAC5G;;;;;;0DAGD,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAW,CAAC,0BAA0B,EAAE,aAAa,OAAO,8BAA8B,iBAAiB;0DAC5G;;;;;;;;;;;;kDAIH,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,MAAK;gDAAS,WAAU;0DACxB,EAAE,KAAK;;;;;;0DAEV,8OAAC;gDAAE,MAAK;gDAAU,WAAU;0DACzB,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7B", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Sharyou/sharyou-landing/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { SparklesIcon, ArrowRightIcon, PlayIcon } from '@heroicons/react/24/outline';\nimport { useState } from 'react';\n\ninterface HeroSectionProps {\n  language: 'fr' | 'ar';\n}\n\nconst translations = {\n  fr: {\n    badge: 'Propulsé par l\\'IA',\n    title: 'Créez votre boutique en ligne en',\n    titleHighlight: '30 secondes',\n    titleEnd: 'avec l\\'IA',\n    subtitle: 'Sharyou révolutionne l\\'e-commerce en Algérie. Notre IA génère automatiquement votre boutique complète, optimisée et prête à vendre.',\n    cta1: 'Créer ma boutique IA',\n    cta2: 'Voir la démo',\n    stats: {\n      stores: '10,000+',\n      storesLabel: 'Boutiques créées',\n      time: '30 sec',\n      timeLabel: 'Temps moyen',\n      success: '95%',\n      successLabel: 'Taux de réussite'\n    }\n  },\n  ar: {\n    badge: 'مدعوم بالذكاء الاصطناعي',\n    title: 'أنشئ متجرك الإلكتروني في',\n    titleHighlight: '30 ثانية',\n    titleEnd: 'بالذكاء الاصطناعي',\n    subtitle: 'شاريو يثور التجارة الإلكترونية في الجزائر. ذكاؤنا الاصطناعي ينشئ متجرك الكامل تلقائياً، محسّن وجاهز للبيع.',\n    cta1: 'إنشاء متجري بالذكاء الاصطناعي',\n    cta2: 'مشاهدة العرض التوضيحي',\n    stats: {\n      stores: '10,000+',\n      storesLabel: 'متجر تم إنشاؤه',\n      time: '30 ثانية',\n      timeLabel: 'متوسط الوقت',\n      success: '95%',\n      successLabel: 'معدل النجاح'\n    }\n  }\n};\n\nexport default function HeroSection({ language }: HeroSectionProps) {\n  const [isVideoPlaying, setIsVideoPlaying] = useState(false);\n  const t = translations[language];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n        delayChildren: 0.3\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 30, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 pt-16\">\n      <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n      \n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          className=\"text-center\"\n        >\n          <motion.div variants={itemVariants} className=\"mb-8\">\n            <span className=\"inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 text-sm font-medium\">\n              <SparklesIcon className=\"w-4 h-4 mr-2\" />\n              {t.badge}\n            </span>\n          </motion.div>\n\n          <motion.h1\n            variants={itemVariants}\n            className=\"text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight\"\n          >\n            {t.title}{' '}\n            <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n              {t.titleHighlight}\n            </span>\n            <br />\n            {t.titleEnd}\n          </motion.h1>\n\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed\"\n          >\n            {t.subtitle}\n          </motion.p>\n\n          <motion.div\n            variants={itemVariants}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-16\"\n          >\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"btn-primary text-lg px-8 py-4 flex items-center group\"\n            >\n              {t.cta1}\n              <ArrowRightIcon className=\"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform\" />\n            </motion.button>\n\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setIsVideoPlaying(true)}\n              className=\"btn-secondary text-lg px-8 py-4 flex items-center group\"\n            >\n              <PlayIcon className=\"w-5 h-5 mr-2 group-hover:scale-110 transition-transform\" />\n              {t.cta2}\n            </motion.button>\n          </motion.div>\n\n          <motion.div\n            variants={itemVariants}\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto mb-16\"\n          >\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-blue-600 mb-2\">{t.stats.stores}</div>\n              <div className=\"text-gray-600\">{t.stats.storesLabel}</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-green-600 mb-2\">{t.stats.time}</div>\n              <div className=\"text-gray-600\">{t.stats.timeLabel}</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-purple-600 mb-2\">{t.stats.success}</div>\n              <div className=\"text-gray-600\">{t.stats.successLabel}</div>\n            </div>\n          </motion.div>\n\n          <motion.div\n            variants={itemVariants}\n            className=\"relative max-w-4xl mx-auto\"\n          >\n            <div className=\"relative rounded-2xl overflow-hidden shadow-2xl bg-white p-2\">\n              <div className=\"aspect-video bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl flex items-center justify-center relative overflow-hidden\">\n                {!isVideoPlaying ? (\n                  <motion.button\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    onClick={() => setIsVideoPlaying(true)}\n                    className=\"w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-shadow\"\n                  >\n                    <PlayIcon className=\"w-8 h-8 text-blue-600 ml-1\" />\n                  </motion.button>\n                ) : (\n                  <div className=\"w-full h-full bg-gray-900 flex items-center justify-center\">\n                    <div className=\"text-white text-center\">\n                      <div className=\"text-2xl mb-4\">🎬</div>\n                      <p>Vidéo de démonstration</p>\n                      <p className=\"text-sm text-gray-400 mt-2\">\n                        Intégration vidéo à venir\n                      </p>\n                    </div>\n                  </div>\n                )}\n                \n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent pointer-events-none\"></div>\n              </div>\n            </div>\n            \n            <div className=\"absolute -top-4 -left-4 w-24 h-24 bg-yellow-400 rounded-full opacity-20 animate-pulse\"></div>\n            <div className=\"absolute -bottom-4 -right-4 w-32 h-32 bg-blue-400 rounded-full opacity-20 animate-pulse delay-1000\"></div>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\">\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center\"\n        >\n          <div className=\"w-1 h-3 bg-gray-400 rounded-full mt-2\"></div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAIA;AAJA;;;;;AAUA,MAAM,eAAe;IACnB,IAAI;QACF,OAAO;QACP,OAAO;QACP,gBAAgB;QAChB,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,OAAO;YACL,QAAQ;YACR,aAAa;YACb,MAAM;YACN,WAAW;YACX,SAAS;YACT,cAAc;QAChB;IACF;IACA,IAAI;QACF,OAAO;QACP,OAAO;QACP,gBAAgB;QAChB,UAAU;QACV,UAAU;QACV,MAAM;QACN,MAAM;QACN,OAAO;YACL,QAAQ;YACR,aAAa;YACb,MAAM;YACN,WAAW;YACX,SAAS;YACT,cAAc;QAChB;IACF;AACF;AAEe,SAAS,YAAY,EAAE,QAAQ,EAAoB;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,IAAI,YAAY,CAAC,SAAS;IAEhC,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,OAAO,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,WAAU;;sCAEV,8OAAC,OAAO,GAAG;4BAAC,UAAU;4BAAc,WAAU;sCAC5C,cAAA,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;wCAAa,WAAU;;;;;;oCACvB,EAAE,KAAK;;;;;;;;;;;;sCAIZ,8OAAC,OAAO,EAAE;4BACR,UAAU;4BACV,WAAU;;gCAET,EAAE,KAAK;gCAAE;8CACV,8OAAC;oCAAK,WAAU;8CACb,EAAE,cAAc;;;;;;8CAEnB,8OAAC;;;;;gCACA,EAAE,QAAQ;;;;;;;sCAGb,8OAAC,OAAO,CAAC;4BACP,UAAU;4BACV,WAAU;sCAET,EAAE,QAAQ;;;;;;sCAGb,8OAAC,OAAO,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC,OAAO,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;;wCAET,EAAE,IAAI;sDACP,8OAAC;4CAAe,WAAU;;;;;;;;;;;;8CAG5B,8OAAC,OAAO,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,kBAAkB;oCACjC,WAAU;;sDAEV,8OAAC;4CAAS,WAAU;;;;;;wCACnB,EAAE,IAAI;;;;;;;;;;;;;sCAIX,8OAAC,OAAO,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAyC,EAAE,KAAK,CAAC,MAAM;;;;;;sDACtE,8OAAC;4CAAI,WAAU;sDAAiB,EAAE,KAAK,CAAC,WAAW;;;;;;;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA0C,EAAE,KAAK,CAAC,IAAI;;;;;;sDACrE,8OAAC;4CAAI,WAAU;sDAAiB,EAAE,KAAK,CAAC,SAAS;;;;;;;;;;;;8CAEnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA2C,EAAE,KAAK,CAAC,OAAO;;;;;;sDACzE,8OAAC;4CAAI,WAAU;sDAAiB,EAAE,KAAK,CAAC,YAAY;;;;;;;;;;;;;;;;;;sCAIxD,8OAAC,OAAO,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,CAAC,+BACA,8OAAC,OAAO,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAI;gDACzB,UAAU;oDAAE,OAAO;gDAAI;gDACvB,SAAS,IAAM,kBAAkB;gDACjC,WAAU;0DAEV,cAAA,8OAAC;oDAAS,WAAU;;;;;;;;;;qEAGtB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,8OAAC;sEAAE;;;;;;sEACH,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;0DAOhD,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAInB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAKrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,OAAO,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 922, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Sharyou/sharyou-landing/src/components/AIBuilderSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState, useEffect } from 'react';\nimport { \n  ChatBubbleLeftRightIcon, \n  PaintBrushIcon, \n  CogIcon, \n  RocketLaunchIcon,\n  CheckIcon \n} from '@heroicons/react/24/outline';\n\ninterface AIBuilderSectionProps {\n  language: 'fr' | 'ar';\n}\n\nconst translations = {\n  fr: {\n    title: 'Comment l\\'IA crée votre boutique',\n    subtitle: 'Un processus révolutionnaire en 4 étapes simples',\n    steps: [\n      {\n        icon: ChatBubbleLeftRightIcon,\n        title: 'Décrivez votre vision',\n        description: 'Parlez à notre IA de votre produit, votre marque et vos objectifs en quelques mots.',\n        time: '10 sec'\n      },\n      {\n        icon: PaintBrushIcon,\n        title: 'IA génère le design',\n        description: 'Notre intelligence artificielle crée un design unique adapté à votre secteur.',\n        time: '15 sec'\n      },\n      {\n        icon: CogIcon,\n        title: 'Configuration automatique',\n        description: 'Paie<PERSON>, livraison, SEO - tout est configuré automatiquement pour l\\'Algérie.',\n        time: '5 sec'\n      },\n      {\n        icon: RocketLaunchIcon,\n        title: 'Boutique prête',\n        description: 'Votre boutique est en ligne, optimisée et prête à recevoir vos premiers clients.',\n        time: '0 sec'\n      }\n    ],\n    stats: {\n      speed: '10x plus rapide',\n      speedDesc: 'qu\\'une création manuelle',\n      accuracy: '99% de précision',\n      accuracyDesc: 'dans la génération',\n      optimization: 'SEO optimisé',\n      optimizationDesc: 'automatiquement'\n    },\n    cta: 'Essayer l\\'IA Builder'\n  },\n  ar: {\n    title: 'كيف ينشئ الذكاء الاصطناعي متجرك',\n    subtitle: 'عملية ثورية في 4 خطوات بسيطة',\n    steps: [\n      {\n        icon: ChatBubbleLeftRightIcon,\n        title: 'صف رؤيتك',\n        description: 'أخبر ذكاءنا الاصطناعي عن منتجك وعلامتك التجارية وأهدافك في كلمات قليلة.',\n        time: '10 ثواني'\n      },\n      {\n        icon: PaintBrushIcon,\n        title: 'الذكاء الاصطناعي ينشئ التصميم',\n        description: 'ذكاؤنا الاصطناعي ينشئ تصميماً فريداً مناسباً لقطاعك.',\n        time: '15 ثانية'\n      },\n      {\n        icon: CogIcon,\n        title: 'التكوين التلقائي',\n        description: 'المدفوعات، التوصيل، السيو - كل شيء مُكوّن تلقائياً للجزائر.',\n        time: '5 ثواني'\n      },\n      {\n        icon: RocketLaunchIcon,\n        title: 'المتجر جاهز',\n        description: 'متجرك متاح على الإنترنت، محسّن وجاهز لاستقبال عملائك الأوائل.',\n        time: '0 ثانية'\n      }\n    ],\n    stats: {\n      speed: 'أسرع 10 مرات',\n      speedDesc: 'من الإنشاء اليدوي',\n      accuracy: '99% دقة',\n      accuracyDesc: 'في التوليد',\n      optimization: 'محسّن للسيو',\n      optimizationDesc: 'تلقائياً'\n    },\n    cta: 'جرب منشئ الذكاء الاصطناعي'\n  }\n};\n\nexport default function AIBuilderSection({ language }: AIBuilderSectionProps) {\n  const [activeStep, setActiveStep] = useState(0);\n  const [isVisible, setIsVisible] = useState(false);\n  const t = translations[language];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setActiveStep((prev) => (prev + 1) % t.steps.length);\n    }, 3000);\n\n    return () => clearInterval(interval);\n  }, [t.steps.length]);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n        delayChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 50, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  return (\n    <section id=\"ai-builder\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-3xl md:text-5xl font-bold text-gray-900 mb-6\"\n          >\n            {t.title}\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-gray-600 max-w-3xl mx-auto\"\n          >\n            {t.subtitle}\n          </motion.p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n          <motion.div\n            variants={containerVariants}\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={{ once: true, amount: 0.3 }}\n            className=\"space-y-8\"\n          >\n            {t.steps.map((step, index) => {\n              const Icon = step.icon;\n              const isActive = index === activeStep;\n              const isCompleted = index < activeStep;\n\n              return (\n                <motion.div\n                  key={index}\n                  variants={itemVariants}\n                  className={`relative flex items-start space-x-4 p-6 rounded-2xl transition-all duration-500 ${\n                    isActive \n                      ? 'bg-blue-50 border-2 border-blue-200 shadow-lg' \n                      : isCompleted \n                        ? 'bg-green-50 border-2 border-green-200' \n                        : 'bg-gray-50 border-2 border-transparent'\n                  }`}\n                >\n                  <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center transition-all duration-500 ${\n                    isActive \n                      ? 'bg-blue-600 text-white' \n                      : isCompleted \n                        ? 'bg-green-600 text-white' \n                        : 'bg-gray-300 text-gray-600'\n                  }`}>\n                    {isCompleted ? (\n                      <CheckIcon className=\"w-6 h-6\" />\n                    ) : (\n                      <Icon className=\"w-6 h-6\" />\n                    )}\n                  </div>\n\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h3 className={`text-lg font-semibold transition-colors duration-500 ${\n                        isActive ? 'text-blue-900' : isCompleted ? 'text-green-900' : 'text-gray-700'\n                      }`}>\n                        {step.title}\n                      </h3>\n                      <span className={`text-sm font-medium px-2 py-1 rounded-full transition-all duration-500 ${\n                        isActive \n                          ? 'bg-blue-100 text-blue-700' \n                          : isCompleted \n                            ? 'bg-green-100 text-green-700' \n                            : 'bg-gray-200 text-gray-600'\n                      }`}>\n                        {step.time}\n                      </span>\n                    </div>\n                    <p className={`transition-colors duration-500 ${\n                      isActive ? 'text-blue-700' : isCompleted ? 'text-green-700' : 'text-gray-600'\n                    }`}>\n                      {step.description}\n                    </p>\n                  </div>\n\n                  {isActive && (\n                    <motion.div\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      className=\"absolute -left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-600 rounded-full\"\n                    />\n                  )}\n                </motion.div>\n              );\n            })}\n          </motion.div>\n\n          <motion.div\n            variants={containerVariants}\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={{ once: true, amount: 0.3 }}\n            className=\"relative\"\n          >\n            <div className=\"bg-gradient-to-br from-blue-600 to-purple-600 rounded-3xl p-8 text-white relative overflow-hidden\">\n              <div className=\"absolute inset-0 bg-grid-pattern opacity-10\"></div>\n              \n              <motion.div variants={itemVariants} className=\"relative z-10\">\n                <h3 className=\"text-2xl font-bold mb-8\">Statistiques IA</h3>\n                \n                <div className=\"space-y-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <div className=\"text-3xl font-bold\">{t.stats.speed}</div>\n                      <div className=\"text-blue-100\">{t.stats.speedDesc}</div>\n                    </div>\n                    <div className=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\">\n                      <RocketLaunchIcon className=\"w-8 h-8\" />\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <div className=\"text-3xl font-bold\">{t.stats.accuracy}</div>\n                      <div className=\"text-blue-100\">{t.stats.accuracyDesc}</div>\n                    </div>\n                    <div className=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\">\n                      <CheckIcon className=\"w-8 h-8\" />\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <div className=\"text-3xl font-bold\">{t.stats.optimization}</div>\n                      <div className=\"text-blue-100\">{t.stats.optimizationDesc}</div>\n                    </div>\n                    <div className=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\">\n                      <CogIcon className=\"w-8 h-8\" />\n                    </div>\n                  </div>\n                </div>\n\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"w-full mt-8 bg-white text-blue-600 font-semibold py-4 px-6 rounded-xl hover:bg-gray-50 transition-colors\"\n                >\n                  {t.cta}\n                </motion.button>\n              </motion.div>\n\n              <div className=\"absolute -top-8 -right-8 w-32 h-32 bg-white/10 rounded-full\"></div>\n              <div className=\"absolute -bottom-8 -left-8 w-24 h-24 bg-white/10 rounded-full\"></div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAGA;;;;;;AAHA;;;;;AAgBA,MAAM,eAAe;IACnB,IAAI;QACF,OAAO;QACP,UAAU;QACV,OAAO;YACL;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,MAAM;YACR;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,MAAM;YACR;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,MAAM;YACR;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,MAAM;YACR;SACD;QACD,OAAO;YACL,OAAO;YACP,WAAW;YACX,UAAU;YACV,cAAc;YACd,cAAc;YACd,kBAAkB;QACpB;QACA,KAAK;IACP;IACA,IAAI;QACF,OAAO;QACP,UAAU;QACV,OAAO;YACL;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,MAAM;YACR;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,MAAM;YACR;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,MAAM;YACR;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,MAAM;YACR;SACD;QACD,OAAO;YACL,OAAO;YACP,WAAW;YACX,UAAU;YACV,cAAc;YACd,cAAc;YACd,kBAAkB;QACpB;QACA,KAAK;IACP;AACF;AAEe,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAC1E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,IAAI,YAAY,CAAC,SAAS;IAEhC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,cAAc,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM;QACrD,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,EAAE,KAAK,CAAC,MAAM;KAAC;IAEnB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAa,WAAU;kBACjC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,OAAO,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,WAAU;;sCAEV,8OAAC,OAAO,EAAE;4BACR,UAAU;4BACV,WAAU;sCAET,EAAE,KAAK;;;;;;sCAEV,8OAAC,OAAO,CAAC;4BACP,UAAU;4BACV,WAAU;sCAET,EAAE,QAAQ;;;;;;;;;;;;8BAIf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,OAAO,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,WAAU;sCAET,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM;gCAClB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,UAAU;gCAC3B,MAAM,cAAc,QAAQ;gCAE5B,qBACE,8OAAC,OAAO,GAAG;oCAET,UAAU;oCACV,WAAW,CAAC,gFAAgF,EAC1F,WACI,kDACA,cACE,0CACA,0CACN;;sDAEF,8OAAC;4CAAI,WAAW,CAAC,kGAAkG,EACjH,WACI,2BACA,cACE,4BACA,6BACN;sDACC,4BACC,8OAAC;gDAAU,WAAU;;;;;qEAErB,8OAAC;gDAAK,WAAU;;;;;;;;;;;sDAIpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAW,CAAC,qDAAqD,EACnE,WAAW,kBAAkB,cAAc,mBAAmB,iBAC9D;sEACC,KAAK,KAAK;;;;;;sEAEb,8OAAC;4DAAK,WAAW,CAAC,uEAAuE,EACvF,WACI,8BACA,cACE,gCACA,6BACN;sEACC,KAAK,IAAI;;;;;;;;;;;;8DAGd,8OAAC;oDAAE,WAAW,CAAC,+BAA+B,EAC5C,WAAW,kBAAkB,cAAc,mBAAmB,iBAC9D;8DACC,KAAK,WAAW;;;;;;;;;;;;wCAIpB,0BACC,8OAAC,OAAO,GAAG;4CACT,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,WAAU;;;;;;;mCApDT;;;;;4BAyDX;;;;;;sCAGF,8OAAC,OAAO,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDAEf,8OAAC,OAAO,GAAG;wCAAC,UAAU;wCAAc,WAAU;;0DAC5C,8OAAC;gDAAG,WAAU;0DAA0B;;;;;;0DAExC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAsB,EAAE,KAAK,CAAC,KAAK;;;;;;kFAClD,8OAAC;wEAAI,WAAU;kFAAiB,EAAE,KAAK,CAAC,SAAS;;;;;;;;;;;;0EAEnD,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAiB,WAAU;;;;;;;;;;;;;;;;;kEAIhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAsB,EAAE,KAAK,CAAC,QAAQ;;;;;;kFACrD,8OAAC;wEAAI,WAAU;kFAAiB,EAAE,KAAK,CAAC,YAAY;;;;;;;;;;;;0EAEtD,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAU,WAAU;;;;;;;;;;;;;;;;;kEAIzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAsB,EAAE,KAAK,CAAC,YAAY;;;;;;kFACzD,8OAAC;wEAAI,WAAU;kFAAiB,EAAE,KAAK,CAAC,gBAAgB;;;;;;;;;;;;0EAE1D,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAQ,WAAU;;;;;;;;;;;;;;;;;;;;;;;0DAKzB,8OAAC,OAAO,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;0DAET,EAAE,GAAG;;;;;;;;;;;;kDAIV,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B", "debugId": null}}, {"offset": {"line": 1457, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Sharyou/sharyou-landing/src/components/FeaturesSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { \n  CursorArrowRaysIcon,\n  PaintBrushIcon,\n  DevicePhoneMobileIcon,\n  GlobeAltIcon,\n  CreditCardIcon,\n  ChartBarIcon\n} from '@heroicons/react/24/outline';\n\ninterface FeaturesSectionProps {\n  language: 'fr' | 'ar';\n}\n\nconst translations = {\n  fr: {\n    title: 'Tout ce dont vous avez besoin',\n    subtitle: 'Une plateforme complète pour réussir votre e-commerce en Algérie',\n    features: [\n      {\n        icon: CursorArrowRaysIcon,\n        title: 'Interface Drag & Drop',\n        description: 'Personnalisez votre boutique facilement avec notre éditeur visuel intuitif. Aucune compétence technique requise.'\n      },\n      {\n        icon: PaintBrushIcon,\n        title: 'Personnalisation Avancée',\n        description: 'Des milliers de templates et options de personnalisation pour créer une boutique unique à votre image.'\n      },\n      {\n        icon: DevicePhoneMobileIcon,\n        title: 'Optimisation Mobile',\n        description: 'Votre boutique s\\'adapte parfaitement à tous les écrans. Plus de 70% de vos clients achètent sur mobile.'\n      },\n      {\n        icon: GlobeAltIcon,\n        title: 'Support Multilingue',\n        description: 'Français, Arabe, Berbère - Vendez dans toutes les langues parlées en Algérie avec traduction automatique.'\n      },\n      {\n        icon: CreditCardIcon,\n        title: 'Paiements Locaux',\n        description: 'CCP, Baridimob, Carte Edahabia - Tous les moyens de paiement algériens intégrés nativement.'\n      },\n      {\n        icon: ChartBarIcon,\n        title: 'Analytics Avancés',\n        description: 'Suivez vos ventes, analysez vos clients et optimisez votre business avec des rapports détaillés.'\n      }\n    ]\n  },\n  ar: {\n    title: 'كل ما تحتاجه',\n    subtitle: 'منصة شاملة لنجاح تجارتك الإلكترونية في الجزائر',\n    features: [\n      {\n        icon: CursorArrowRaysIcon,\n        title: 'واجهة السحب والإفلات',\n        description: 'خصص متجرك بسهولة مع محررنا المرئي البديهي. لا حاجة لمهارات تقنية.'\n      },\n      {\n        icon: PaintBrushIcon,\n        title: 'تخصيص متقدم',\n        description: 'آلاف القوالب وخيارات التخصيص لإنشاء متجر فريد يعكس هويتك.'\n      },\n      {\n        icon: DevicePhoneMobileIcon,\n        title: 'تحسين للهاتف المحمول',\n        description: 'متجرك يتكيف تماماً مع جميع الشاشات. أكثر من 70% من عملائك يشترون عبر الهاتف.'\n      },\n      {\n        icon: GlobeAltIcon,\n        title: 'دعم متعدد اللغات',\n        description: 'الفرنسية، العربية، الأمازيغية - بع بجميع اللغات المحكية في الجزائر مع ترجمة تلقائية.'\n      },\n      {\n        icon: CreditCardIcon,\n        title: 'مدفوعات محلية',\n        description: 'الحساب الجاري البريدي، بريدي موب، بطاقة الذهبية - جميع وسائل الدفع الجزائرية مدمجة أصلياً.'\n      },\n      {\n        icon: ChartBarIcon,\n        title: 'تحليلات متقدمة',\n        description: 'تتبع مبيعاتك، حلل عملاءك وحسّن عملك مع تقارير مفصلة.'\n      }\n    ]\n  }\n};\n\nexport default function FeaturesSection({ language }: FeaturesSectionProps) {\n  const t = translations[language];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 50, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  const cardVariants = {\n    hidden: { y: 50, opacity: 0, scale: 0.9 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      scale: 1,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  return (\n    <section id=\"features\" className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-3xl md:text-5xl font-bold text-gray-900 mb-6\"\n          >\n            {t.title}\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-gray-600 max-w-3xl mx-auto\"\n          >\n            {t.subtitle}\n          </motion.p>\n        </motion.div>\n\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.2 }}\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\n        >\n          {t.features.map((feature, index) => {\n            const Icon = feature.icon;\n            \n            return (\n              <motion.div\n                key={index}\n                variants={cardVariants}\n                whileHover={{ \n                  y: -10,\n                  transition: { duration: 0.3 }\n                }}\n                className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100 group\"\n              >\n                <div className=\"mb-6\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                    <Icon className=\"w-8 h-8 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                    {feature.title}\n                  </h3>\n                  <p className=\"text-gray-600 leading-relaxed\">\n                    {feature.description}\n                  </p>\n                </div>\n\n                <div className=\"pt-4 border-t border-gray-100\">\n                  <motion.div\n                    initial={{ width: 0 }}\n                    whileInView={{ width: '100%' }}\n                    transition={{ duration: 1, delay: index * 0.1 }}\n                    className=\"h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full\"\n                  />\n                </div>\n              </motion.div>\n            );\n          })}\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"mt-16 text-center\"\n        >\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-12 text-white relative overflow-hidden\">\n            <div className=\"absolute inset-0 bg-grid-pattern opacity-10\"></div>\n            \n            <div className=\"relative z-10\">\n              <h3 className=\"text-2xl md:text-3xl font-bold mb-4\">\n                {language === 'fr' ? 'Prêt à révolutionner votre e-commerce ?' : 'مستعد لثورة في تجارتك الإلكترونية؟'}\n              </h3>\n              <p className=\"text-xl text-blue-100 mb-8 max-w-2xl mx-auto\">\n                {language === 'fr' \n                  ? 'Rejoignez les milliers d\\'entrepreneurs algériens qui ont choisi Sharyou pour développer leur business en ligne.'\n                  : 'انضم إلى آلاف رجال الأعمال الجزائريين الذين اختاروا شاريو لتطوير أعمالهم عبر الإنترنت.'\n                }\n              </p>\n              \n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"bg-white text-blue-600 font-semibold py-4 px-8 rounded-xl hover:bg-gray-50 transition-colors\"\n                >\n                  {language === 'fr' ? 'Commencer maintenant' : 'ابدأ الآن'}\n                </motion.button>\n                \n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"border-2 border-white text-white font-semibold py-4 px-8 rounded-xl hover:bg-white hover:text-blue-600 transition-colors\"\n                >\n                  {language === 'fr' ? 'Voir les tarifs' : 'عرض الأسعار'}\n                </motion.button>\n              </div>\n            </div>\n\n            <div className=\"absolute -top-8 -right-8 w-32 h-32 bg-white/10 rounded-full\"></div>\n            <div className=\"absolute -bottom-8 -left-8 w-24 h-24 bg-white/10 rounded-full\"></div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;;AAgBA,MAAM,eAAe;IACnB,IAAI;QACF,OAAO;QACP,UAAU;QACV,UAAU;YACR;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;SACD;IACH;IACA,IAAI;QACF,OAAO;QACP,UAAU;QACV,UAAU;YACR;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;SACD;IACH;AACF;AAEe,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,MAAM,IAAI,YAAY,CAAC,SAAS;IAEhC,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;YAAG,OAAO;QAAI;QACxC,SAAS;YACP,GAAG;YACH,SAAS;YACT,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,OAAO,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,WAAU;;sCAEV,8OAAC,OAAO,EAAE;4BACR,UAAU;4BACV,WAAU;sCAET,EAAE,KAAK;;;;;;sCAEV,8OAAC,OAAO,CAAC;4BACP,UAAU;4BACV,WAAU;sCAET,EAAE,QAAQ;;;;;;;;;;;;8BAIf,8OAAC,OAAO,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,WAAU;8BAET,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS;wBACxB,MAAM,OAAO,QAAQ,IAAI;wBAEzB,qBACE,8OAAC,OAAO,GAAG;4BAET,UAAU;4BACV,YAAY;gCACV,GAAG,CAAC;gCACJ,YAAY;oCAAE,UAAU;gCAAI;4BAC9B;4BACA,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;;;;;;;8CAIxB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,OAAO,GAAG;wCACT,SAAS;4CAAE,OAAO;wCAAE;wCACpB,aAAa;4CAAE,OAAO;wCAAO;wCAC7B,YAAY;4CAAE,UAAU;4CAAG,OAAO,QAAQ;wCAAI;wCAC9C,WAAU;;;;;;;;;;;;2BAzBT;;;;;oBA8BX;;;;;;8BAGF,8OAAC,OAAO,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,aAAa,OAAO,4CAA4C;;;;;;kDAEnE,8OAAC;wCAAE,WAAU;kDACV,aAAa,OACV,qHACA;;;;;;kDAIN,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,OAAO,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;0DAET,aAAa,OAAO,yBAAyB;;;;;;0DAGhD,8OAAC,OAAO,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;0DAET,aAAa,OAAO,oBAAoB;;;;;;;;;;;;;;;;;;0CAK/C,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}, {"offset": {"line": 1861, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Sharyou/sharyou-landing/src/components/TestimonialsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState, useEffect } from 'react';\nimport { StarIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/solid';\n\ninterface TestimonialsSectionProps {\n  language: 'fr' | 'ar';\n}\n\nconst translations = {\n  fr: {\n    title: 'Ils ont réussi avec Sharyou',\n    subtitle: 'Découvrez les success stories d\\'entrepreneurs algériens',\n    testimonials: [\n      {\n        name: '<PERSON><PERSON>',\n        role: 'Fondatrice, Bijoux Berbères',\n        location: '<PERSON>ger',\n        image: '/testimonials/amina.jpg',\n        content: 'Sharyou a transformé ma passion pour les bijoux traditionnels en véritable business. En 30 secondes, j\\'avais ma boutique en ligne. Aujourd\\'hui, je vends dans toute l\\'Algérie !',\n        revenue: '+300% de ventes',\n        timeframe: 'en 3 mois'\n      },\n      {\n        name: '<PERSON><PERSON>',\n        role: 'CEO, TechStore DZ',\n        location: '<PERSON><PERSON>',\n        image: '/testimonials/karim.jpg',\n        content: 'L\\'IA de Sharyou a créé exactement le design que j\\'imaginais pour ma boutique tech. Le support multilingue m\\'a permis de toucher tous mes clients.',\n        revenue: '50 000 DA/jour',\n        timeframe: 'de chiffre d\\'affaires'\n      },\n      {\n        name: 'Fatima Zohra',\n        role: 'Artisane, Poterie Kabyle',\n        location: 'Tizi Ouzou',\n        image: '/testimonials/fatima.jpg',\n        content: 'Je ne connaissais rien au web. Avec Sharyou, j\\'ai pu créer ma boutique et vendre mes poteries dans le monde entier. C\\'est magique !',\n        revenue: 'Export international',\n        timeframe: 'dès le 1er mois'\n      }\n    ],\n    metrics: {\n      satisfaction: '98%',\n      satisfactionLabel: 'Satisfaction client',\n      growth: '+250%',\n      growthLabel: 'Croissance moyenne',\n      support: '24/7',\n      supportLabel: 'Support technique'\n    }\n  },\n  ar: {\n    title: 'نجحوا مع شاريو',\n    subtitle: 'اكتشف قصص نجاح رجال الأعمال الجزائريين',\n    testimonials: [\n      {\n        name: 'أمينة بن علي',\n        role: 'مؤسسة، مجوهرات أمازيغية',\n        location: 'الجزائر العاصمة',\n        image: '/testimonials/amina.jpg',\n        content: 'شاريو حوّل شغفي بالمجوهرات التقليدية إلى عمل حقيقي. في 30 ثانية، كان لدي متجري الإلكتروني. اليوم، أبيع في كل الجزائر!',\n        revenue: '+300% في المبيعات',\n        timeframe: 'في 3 أشهر'\n      },\n      {\n        name: 'كريم مسعودي',\n        role: 'المدير التنفيذي، تك ستور دي زد',\n        location: 'وهران',\n        image: '/testimonials/karim.jpg',\n        content: 'ذكاء شاريو الاصطناعي أنشأ بالضبط التصميم الذي تخيلته لمتجر التكنولوجيا. الدعم متعدد اللغات مكنني من الوصول لجميع عملائي.',\n        revenue: '50,000 دج/يوم',\n        timeframe: 'من رقم الأعمال'\n      },\n      {\n        name: 'فاطمة الزهراء',\n        role: 'حرفية، فخار قبائلي',\n        location: 'تيزي وزو',\n        image: '/testimonials/fatima.jpg',\n        content: 'لم أكن أعرف شيئاً عن الويب. مع شاريو، تمكنت من إنشاء متجري وبيع فخارياتي في العالم كله. إنه سحر!',\n        revenue: 'تصدير دولي',\n        timeframe: 'من الشهر الأول'\n      }\n    ],\n    metrics: {\n      satisfaction: '98%',\n      satisfactionLabel: 'رضا العملاء',\n      growth: '+250%',\n      growthLabel: 'النمو المتوسط',\n      support: '24/7',\n      supportLabel: 'الدعم التقني'\n    }\n  }\n};\n\nexport default function TestimonialsSection({ language }: TestimonialsSectionProps) {\n  const [currentTestimonial, setCurrentTestimonial] = useState(0);\n  const t = translations[language];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentTestimonial((prev) => (prev + 1) % t.testimonials.length);\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [t.testimonials.length]);\n\n  const nextTestimonial = () => {\n    setCurrentTestimonial((prev) => (prev + 1) % t.testimonials.length);\n  };\n\n  const prevTestimonial = () => {\n    setCurrentTestimonial((prev) => (prev - 1 + t.testimonials.length) % t.testimonials.length);\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n        delayChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 50, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  return (\n    <section id=\"testimonials\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.3 }}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-3xl md:text-5xl font-bold text-gray-900 mb-6\"\n          >\n            {t.title}\n          </motion.h2>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-gray-600 max-w-3xl mx-auto\"\n          >\n            {t.subtitle}\n          </motion.p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n          <motion.div\n            variants={containerVariants}\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={{ once: true, amount: 0.3 }}\n            className=\"relative\"\n          >\n            <div className=\"bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl p-8 relative overflow-hidden\">\n              <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\n              \n              <motion.div\n                key={currentTestimonial}\n                initial={{ opacity: 0, x: 50 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: -50 }}\n                transition={{ duration: 0.5 }}\n                className=\"relative z-10\"\n              >\n                <div className=\"flex items-center mb-6\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4\">\n                    {t.testimonials[currentTestimonial].name.charAt(0)}\n                  </div>\n                  <div>\n                    <h3 className=\"text-xl font-bold text-gray-900\">\n                      {t.testimonials[currentTestimonial].name}\n                    </h3>\n                    <p className=\"text-gray-600\">\n                      {t.testimonials[currentTestimonial].role}\n                    </p>\n                    <p className=\"text-sm text-gray-500\">\n                      {t.testimonials[currentTestimonial].location}\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"flex mb-4\">\n                  {[...Array(5)].map((_, i) => (\n                    <StarIcon key={i} className=\"w-5 h-5 text-yellow-400\" />\n                  ))}\n                </div>\n\n                <blockquote className=\"text-lg text-gray-700 mb-6 leading-relaxed\">\n                  \"{t.testimonials[currentTestimonial].content}\"\n                </blockquote>\n\n                <div className=\"flex items-center justify-between bg-white rounded-xl p-4\">\n                  <div>\n                    <div className=\"text-2xl font-bold text-green-600\">\n                      {t.testimonials[currentTestimonial].revenue}\n                    </div>\n                    <div className=\"text-sm text-gray-600\">\n                      {t.testimonials[currentTestimonial].timeframe}\n                    </div>\n                  </div>\n                  <div className=\"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center\">\n                    <span className=\"text-2xl\">📈</span>\n                  </div>\n                </div>\n              </motion.div>\n\n              <div className=\"absolute -top-4 -right-4 w-24 h-24 bg-blue-200 rounded-full opacity-20\"></div>\n              <div className=\"absolute -bottom-4 -left-4 w-32 h-32 bg-purple-200 rounded-full opacity-20\"></div>\n            </div>\n\n            <div className=\"flex justify-center items-center mt-6 space-x-4\">\n              <button\n                onClick={prevTestimonial}\n                className=\"w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors\"\n              >\n                <ChevronLeftIcon className=\"w-5 h-5 text-gray-600\" />\n              </button>\n\n              <div className=\"flex space-x-2\">\n                {t.testimonials.map((_, index) => (\n                  <button\n                    key={index}\n                    onClick={() => setCurrentTestimonial(index)}\n                    className={`w-3 h-3 rounded-full transition-colors ${\n                      index === currentTestimonial ? 'bg-blue-600' : 'bg-gray-300'\n                    }`}\n                  />\n                ))}\n              </div>\n\n              <button\n                onClick={nextTestimonial}\n                className=\"w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors\"\n              >\n                <ChevronRightIcon className=\"w-5 h-5 text-gray-600\" />\n              </button>\n            </div>\n          </motion.div>\n\n          <motion.div\n            variants={containerVariants}\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={{ once: true, amount: 0.3 }}\n            className=\"space-y-8\"\n          >\n            <motion.div variants={itemVariants} className=\"text-center\">\n              <div className=\"text-4xl font-bold text-blue-600 mb-2\">{t.metrics.satisfaction}</div>\n              <div className=\"text-gray-600\">{t.metrics.satisfactionLabel}</div>\n            </motion.div>\n\n            <motion.div variants={itemVariants} className=\"text-center\">\n              <div className=\"text-4xl font-bold text-green-600 mb-2\">{t.metrics.growth}</div>\n              <div className=\"text-gray-600\">{t.metrics.growthLabel}</div>\n            </motion.div>\n\n            <motion.div variants={itemVariants} className=\"text-center\">\n              <div className=\"text-4xl font-bold text-purple-600 mb-2\">{t.metrics.support}</div>\n              <div className=\"text-gray-600\">{t.metrics.supportLabel}</div>\n            </motion.div>\n\n            <motion.div\n              variants={itemVariants}\n              className=\"bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl p-6 text-white text-center\"\n            >\n              <h3 className=\"text-xl font-bold mb-2\">\n                {language === 'fr' ? 'Rejoignez-les !' : 'انضم إليهم!'}\n              </h3>\n              <p className=\"mb-4\">\n                {language === 'fr' \n                  ? 'Plus de 10,000 entrepreneurs nous font confiance'\n                  : 'أكثر من 10,000 رجل أعمال يثقون بنا'\n                }\n              </p>\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"bg-white text-blue-600 font-semibold py-3 px-6 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                {language === 'fr' ? 'Commencer gratuitement' : 'ابدأ مجاناً'}\n              </motion.button>\n            </motion.div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAGA;;;;;;AAHA;;;;;AAUA,MAAM,eAAe;IACnB,IAAI;QACF,OAAO;QACP,UAAU;QACV,cAAc;YACZ;gBACE,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,WAAW;YACb;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,WAAW;YACb;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,WAAW;YACb;SACD;QACD,SAAS;YACP,cAAc;YACd,mBAAmB;YACnB,QAAQ;YACR,aAAa;YACb,SAAS;YACT,cAAc;QAChB;IACF;IACA,IAAI;QACF,OAAO;QACP,UAAU;QACV,cAAc;YACZ;gBACE,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,WAAW;YACb;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,WAAW;YACb;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,WAAW;YACb;SACD;QACD,SAAS;YACP,cAAc;YACd,mBAAmB;YACnB,QAAQ;YACR,aAAa;YACb,SAAS;YACT,cAAc;QAChB;IACF;AACF;AAEe,SAAS,oBAAoB,EAAE,QAAQ,EAA4B;IAChF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,IAAI,YAAY,CAAC,SAAS;IAEhC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,sBAAsB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,EAAE,YAAY,CAAC,MAAM;QACpE,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,EAAE,YAAY,CAAC,MAAM;KAAC;IAE1B,MAAM,kBAAkB;QACtB,sBAAsB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,EAAE,YAAY,CAAC,MAAM;IACpE;IAEA,MAAM,kBAAkB;QACtB,sBAAsB,CAAC,OAAS,CAAC,OAAO,IAAI,EAAE,YAAY,CAAC,MAAM,IAAI,EAAE,YAAY,CAAC,MAAM;IAC5F;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAe,WAAU;kBACnC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,OAAO,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,WAAU;;sCAEV,8OAAC,OAAO,EAAE;4BACR,UAAU;4BACV,WAAU;sCAET,EAAE,KAAK;;;;;;sCAEV,8OAAC,OAAO,CAAC;4BACP,UAAU;4BACV,WAAU;sCAET,EAAE,QAAQ;;;;;;;;;;;;8BAIf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,OAAO,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC,OAAO,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,MAAM;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC3B,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,EAAE,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;sEAElD,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EACX,EAAE,YAAY,CAAC,mBAAmB,CAAC,IAAI;;;;;;8EAE1C,8OAAC;oEAAE,WAAU;8EACV,EAAE,YAAY,CAAC,mBAAmB,CAAC,IAAI;;;;;;8EAE1C,8OAAC;oEAAE,WAAU;8EACV,EAAE,YAAY,CAAC,mBAAmB,CAAC,QAAQ;;;;;;;;;;;;;;;;;;8DAKlD,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4DAAiB,WAAU;2DAAb;;;;;;;;;;8DAInB,8OAAC;oDAAW,WAAU;;wDAA6C;wDAC/D,EAAE,YAAY,CAAC,mBAAmB,CAAC,OAAO;wDAAC;;;;;;;8DAG/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EACZ,EAAE,YAAY,CAAC,mBAAmB,CAAC,OAAO;;;;;;8EAE7C,8OAAC;oEAAI,WAAU;8EACZ,EAAE,YAAY,CAAC,mBAAmB,CAAC,SAAS;;;;;;;;;;;;sEAGjD,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAAW;;;;;;;;;;;;;;;;;;2CA5C1B;;;;;sDAiDP,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAGjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC;gDAAgB,WAAU;;;;;;;;;;;sDAG7B,8OAAC;4CAAI,WAAU;sDACZ,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,sBACtB,8OAAC;oDAEC,SAAS,IAAM,sBAAsB;oDACrC,WAAW,CAAC,uCAAuC,EACjD,UAAU,qBAAqB,gBAAgB,eAC/C;mDAJG;;;;;;;;;;sDASX,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC;gDAAiB,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAKlC,8OAAC,OAAO,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU;gCAAE,MAAM;gCAAM,QAAQ;4BAAI;4BACpC,WAAU;;8CAEV,8OAAC,OAAO,GAAG;oCAAC,UAAU;oCAAc,WAAU;;sDAC5C,8OAAC;4CAAI,WAAU;sDAAyC,EAAE,OAAO,CAAC,YAAY;;;;;;sDAC9E,8OAAC;4CAAI,WAAU;sDAAiB,EAAE,OAAO,CAAC,iBAAiB;;;;;;;;;;;;8CAG7D,8OAAC,OAAO,GAAG;oCAAC,UAAU;oCAAc,WAAU;;sDAC5C,8OAAC;4CAAI,WAAU;sDAA0C,EAAE,OAAO,CAAC,MAAM;;;;;;sDACzE,8OAAC;4CAAI,WAAU;sDAAiB,EAAE,OAAO,CAAC,WAAW;;;;;;;;;;;;8CAGvD,8OAAC,OAAO,GAAG;oCAAC,UAAU;oCAAc,WAAU;;sDAC5C,8OAAC;4CAAI,WAAU;sDAA2C,EAAE,OAAO,CAAC,OAAO;;;;;;sDAC3E,8OAAC;4CAAI,WAAU;sDAAiB,EAAE,OAAO,CAAC,YAAY;;;;;;;;;;;;8CAGxD,8OAAC,OAAO,GAAG;oCACT,UAAU;oCACV,WAAU;;sDAEV,8OAAC;4CAAG,WAAU;sDACX,aAAa,OAAO,oBAAoB;;;;;;sDAE3C,8OAAC;4CAAE,WAAU;sDACV,aAAa,OACV,qDACA;;;;;;sDAGN,8OAAC,OAAO,MAAM;4CACZ,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;sDAET,aAAa,OAAO,2BAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhE", "debugId": null}}, {"offset": {"line": 2458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Sharyou/sharyou-landing/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { \n  EnvelopeIcon, \n  PhoneIcon, \n  MapPinIcon,\n  AcademicCapIcon,\n  QuestionMarkCircleIcon,\n  DocumentTextIcon\n} from '@heroicons/react/24/outline';\n\ninterface FooterProps {\n  language: 'fr' | 'ar';\n}\n\nconst translations = {\n  fr: {\n    company: {\n      title: 'Sharyou',\n      description: 'La plateforme e-commerce algérienne propulsée par l\\'IA. Créez votre boutique en ligne en 30 secondes.',\n      contact: {\n        email: '<EMAIL>',\n        phone: '+213 (0) 21 XX XX XX',\n        address: 'Alger, Algérie'\n      }\n    },\n    links: {\n      resources: {\n        title: 'Ressources',\n        items: [\n          { name: '<PERSON> d\\'aide', href: '#help' },\n          { name: 'Tu<PERSON>iels vidéo', href: '#tutorials' },\n          { name: 'Blog', href: '#blog' },\n          { name: 'Webinaires', href: '#webinars' }\n        ]\n      },\n      support: {\n        title: 'Support',\n        items: [\n          { name: 'Support technique', href: '#support' },\n          { name: 'FAQ', href: '#faq' },\n          { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '#community' },\n          { name: 'Status', href: '#status' }\n        ]\n      },\n      legal: {\n        title: 'Légal',\n        items: [\n          { name: 'Conditions d\\'utilisation', href: '#terms' },\n          { name: 'Politique de confidentialité', href: '#privacy' },\n          { name: 'Mentions légales', href: '#legal' },\n          { name: 'RGPD', href: '#gdpr' }\n        ]\n      }\n    },\n    social: {\n      title: 'Suivez-nous',\n      platforms: [\n        { name: 'Facebook', href: '#facebook', icon: '📘' },\n        { name: 'Instagram', href: '#instagram', icon: '📷' },\n        { name: 'LinkedIn', href: '#linkedin', icon: '💼' },\n        { name: 'YouTube', href: '#youtube', icon: '📺' }\n      ]\n    },\n    newsletter: {\n      title: 'Newsletter',\n      description: 'Recevez nos dernières actualités et conseils e-commerce',\n      placeholder: 'Votre email',\n      button: 'S\\'abonner'\n    },\n    copyright: '© 2024 Sharyou. Tous droits réservés.',\n    madeIn: 'Fait avec ❤️ en Algérie'\n  },\n  ar: {\n    company: {\n      title: 'شاريو',\n      description: 'منصة التجارة الإلكترونية الجزائرية المدعومة بالذكاء الاصطناعي. أنشئ متجرك الإلكتروني في 30 ثانية.',\n      contact: {\n        email: '<EMAIL>',\n        phone: '+213 (0) 21 XX XX XX',\n        address: 'الجزائر العاصمة، الجزائر'\n      }\n    },\n    links: {\n      resources: {\n        title: 'الموارد',\n        items: [\n          { name: 'مركز المساعدة', href: '#help' },\n          { name: 'دروس فيديو', href: '#tutorials' },\n          { name: 'المدونة', href: '#blog' },\n          { name: 'ندوات ويب', href: '#webinars' }\n        ]\n      },\n      support: {\n        title: 'الدعم',\n        items: [\n          { name: 'الدعم التقني', href: '#support' },\n          { name: 'الأسئلة الشائعة', href: '#faq' },\n          { name: 'المجتمع', href: '#community' },\n          { name: 'الحالة', href: '#status' }\n        ]\n      },\n      legal: {\n        title: 'قانوني',\n        items: [\n          { name: 'شروط الاستخدام', href: '#terms' },\n          { name: 'سياسة الخصوصية', href: '#privacy' },\n          { name: 'الإشعارات القانونية', href: '#legal' },\n          { name: 'حماية البيانات', href: '#gdpr' }\n        ]\n      }\n    },\n    social: {\n      title: 'تابعنا',\n      platforms: [\n        { name: 'فيسبوك', href: '#facebook', icon: '📘' },\n        { name: 'إنستغرام', href: '#instagram', icon: '📷' },\n        { name: 'لينكد إن', href: '#linkedin', icon: '💼' },\n        { name: 'يوتيوب', href: '#youtube', icon: '📺' }\n      ]\n    },\n    newsletter: {\n      title: 'النشرة الإخبارية',\n      description: 'احصل على آخر أخبارنا ونصائح التجارة الإلكترونية',\n      placeholder: 'بريدك الإلكتروني',\n      button: 'اشتراك'\n    },\n    copyright: '© 2024 شاريو. جميع الحقوق محفوظة.',\n    madeIn: 'صُنع بـ ❤️ في الجزائر'\n  }\n};\n\nexport default function Footer({ language }: FooterProps) {\n  const t = translations[language];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 30, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.2 }}\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12\"\n        >\n          <motion.div variants={itemVariants} className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">S</span>\n              </div>\n              <span className=\"text-xl font-bold\">{t.company.title}</span>\n            </div>\n            <p className=\"text-gray-400 mb-6 leading-relaxed\">\n              {t.company.description}\n            </p>\n            \n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-3\">\n                <EnvelopeIcon className=\"w-5 h-5 text-blue-400\" />\n                <span className=\"text-gray-400\">{t.company.contact.email}</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <PhoneIcon className=\"w-5 h-5 text-blue-400\" />\n                <span className=\"text-gray-400\">{t.company.contact.phone}</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <MapPinIcon className=\"w-5 h-5 text-blue-400\" />\n                <span className=\"text-gray-400\">{t.company.contact.address}</span>\n              </div>\n            </div>\n          </motion.div>\n\n          <motion.div variants={itemVariants}>\n            <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\n              <AcademicCapIcon className=\"w-5 h-5 mr-2 text-blue-400\" />\n              {t.links.resources.title}\n            </h3>\n            <ul className=\"space-y-3\">\n              {t.links.resources.items.map((item, index) => (\n                <li key={index}>\n                  <a \n                    href={item.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {item.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          <motion.div variants={itemVariants}>\n            <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\n              <QuestionMarkCircleIcon className=\"w-5 h-5 mr-2 text-blue-400\" />\n              {t.links.support.title}\n            </h3>\n            <ul className=\"space-y-3\">\n              {t.links.support.items.map((item, index) => (\n                <li key={index}>\n                  <a \n                    href={item.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {item.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          <motion.div variants={itemVariants}>\n            <h3 className=\"text-lg font-semibold mb-4 flex items-center\">\n              <DocumentTextIcon className=\"w-5 h-5 mr-2 text-blue-400\" />\n              {t.links.legal.title}\n            </h3>\n            <ul className=\"space-y-3\">\n              {t.links.legal.items.map((item, index) => (\n                <li key={index}>\n                  <a \n                    href={item.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {item.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n        </motion.div>\n\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.2 }}\n          className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12\"\n        >\n          <motion.div variants={itemVariants}>\n            <h3 className=\"text-lg font-semibold mb-4\">{t.social.title}</h3>\n            <div className=\"flex space-x-4\">\n              {t.social.platforms.map((platform, index) => (\n                <motion.a\n                  key={index}\n                  href={platform.href}\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                  className=\"w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors duration-200\"\n                >\n                  <span className=\"text-xl\">{platform.icon}</span>\n                </motion.a>\n              ))}\n            </div>\n          </motion.div>\n\n          <motion.div variants={itemVariants}>\n            <h3 className=\"text-lg font-semibold mb-4\">{t.newsletter.title}</h3>\n            <p className=\"text-gray-400 mb-4\">{t.newsletter.description}</p>\n            <div className=\"flex\">\n              <input\n                type=\"email\"\n                placeholder={t.newsletter.placeholder}\n                className=\"flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-l-lg focus:outline-none focus:border-blue-500 text-white\"\n              />\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-r-lg font-semibold transition-colors duration-200\"\n              >\n                {t.newsletter.button}\n              </motion.button>\n            </div>\n          </motion.div>\n        </motion.div>\n\n        <motion.div\n          variants={itemVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center\"\n        >\n          <p className=\"text-gray-400 mb-4 md:mb-0\">{t.copyright}</p>\n          <p className=\"text-gray-400\">{t.madeIn}</p>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;;AAgBA,MAAM,eAAe;IACnB,IAAI;QACF,SAAS;YACP,OAAO;YACP,aAAa;YACb,SAAS;gBACP,OAAO;gBACP,OAAO;gBACP,SAAS;YACX;QACF;QACA,OAAO;YACL,WAAW;gBACT,OAAO;gBACP,OAAO;oBACL;wBAAE,MAAM;wBAAkB,MAAM;oBAAQ;oBACxC;wBAAE,MAAM;wBAAmB,MAAM;oBAAa;oBAC9C;wBAAE,MAAM;wBAAQ,MAAM;oBAAQ;oBAC9B;wBAAE,MAAM;wBAAc,MAAM;oBAAY;iBACzC;YACH;YACA,SAAS;gBACP,OAAO;gBACP,OAAO;oBACL;wBAAE,MAAM;wBAAqB,MAAM;oBAAW;oBAC9C;wBAAE,MAAM;wBAAO,MAAM;oBAAO;oBAC5B;wBAAE,MAAM;wBAAc,MAAM;oBAAa;oBACzC;wBAAE,MAAM;wBAAU,MAAM;oBAAU;iBACnC;YACH;YACA,OAAO;gBACL,OAAO;gBACP,OAAO;oBACL;wBAAE,MAAM;wBAA6B,MAAM;oBAAS;oBACpD;wBAAE,MAAM;wBAAgC,MAAM;oBAAW;oBACzD;wBAAE,MAAM;wBAAoB,MAAM;oBAAS;oBAC3C;wBAAE,MAAM;wBAAQ,MAAM;oBAAQ;iBAC/B;YACH;QACF;QACA,QAAQ;YACN,OAAO;YACP,WAAW;gBACT;oBAAE,MAAM;oBAAY,MAAM;oBAAa,MAAM;gBAAK;gBAClD;oBAAE,MAAM;oBAAa,MAAM;oBAAc,MAAM;gBAAK;gBACpD;oBAAE,MAAM;oBAAY,MAAM;oBAAa,MAAM;gBAAK;gBAClD;oBAAE,MAAM;oBAAW,MAAM;oBAAY,MAAM;gBAAK;aACjD;QACH;QACA,YAAY;YACV,OAAO;YACP,aAAa;YACb,aAAa;YACb,QAAQ;QACV;QACA,WAAW;QACX,QAAQ;IACV;IACA,IAAI;QACF,SAAS;YACP,OAAO;YACP,aAAa;YACb,SAAS;gBACP,OAAO;gBACP,OAAO;gBACP,SAAS;YACX;QACF;QACA,OAAO;YACL,WAAW;gBACT,OAAO;gBACP,OAAO;oBACL;wBAAE,MAAM;wBAAiB,MAAM;oBAAQ;oBACvC;wBAAE,MAAM;wBAAc,MAAM;oBAAa;oBACzC;wBAAE,MAAM;wBAAW,MAAM;oBAAQ;oBACjC;wBAAE,MAAM;wBAAa,MAAM;oBAAY;iBACxC;YACH;YACA,SAAS;gBACP,OAAO;gBACP,OAAO;oBACL;wBAAE,MAAM;wBAAgB,MAAM;oBAAW;oBACzC;wBAAE,MAAM;wBAAmB,MAAM;oBAAO;oBACxC;wBAAE,MAAM;wBAAW,MAAM;oBAAa;oBACtC;wBAAE,MAAM;wBAAU,MAAM;oBAAU;iBACnC;YACH;YACA,OAAO;gBACL,OAAO;gBACP,OAAO;oBACL;wBAAE,MAAM;wBAAkB,MAAM;oBAAS;oBACzC;wBAAE,MAAM;wBAAkB,MAAM;oBAAW;oBAC3C;wBAAE,MAAM;wBAAuB,MAAM;oBAAS;oBAC9C;wBAAE,MAAM;wBAAkB,MAAM;oBAAQ;iBACzC;YACH;QACF;QACA,QAAQ;YACN,OAAO;YACP,WAAW;gBACT;oBAAE,MAAM;oBAAU,MAAM;oBAAa,MAAM;gBAAK;gBAChD;oBAAE,MAAM;oBAAY,MAAM;oBAAc,MAAM;gBAAK;gBACnD;oBAAE,MAAM;oBAAY,MAAM;oBAAa,MAAM;gBAAK;gBAClD;oBAAE,MAAM;oBAAU,MAAM;oBAAY,MAAM;gBAAK;aAChD;QACH;QACA,YAAY;YACV,OAAO;YACP,aAAa;YACb,aAAa;YACb,QAAQ;QACV;QACA,WAAW;QACX,QAAQ;IACV;AACF;AAEe,SAAS,OAAO,EAAE,QAAQ,EAAe;IACtD,MAAM,IAAI,YAAY,CAAC,SAAS;IAEhC,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,OAAO,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,WAAU;;sCAEV,8OAAC,OAAO,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAqB,EAAE,OAAO,CAAC,KAAK;;;;;;;;;;;;8CAEtD,8OAAC;oCAAE,WAAU;8CACV,EAAE,OAAO,CAAC,WAAW;;;;;;8CAGxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAa,WAAU;;;;;;8DACxB,8OAAC;oDAAK,WAAU;8DAAiB,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;sDAE1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAU,WAAU;;;;;;8DACrB,8OAAC;oDAAK,WAAU;8DAAiB,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;sDAE1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAW,WAAU;;;;;;8DACtB,8OAAC;oDAAK,WAAU;8DAAiB,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAKhE,8OAAC,OAAO,GAAG;4BAAC,UAAU;;8CACpB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAgB,WAAU;;;;;;wCAC1B,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK;;;;;;;8CAE1B,8OAAC;oCAAG,WAAU;8CACX,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAClC,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL;;;;;;;;;;;;;;;;sCAYf,8OAAC,OAAO,GAAG;4BAAC,UAAU;;8CACpB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAuB,WAAU;;;;;;wCACjC,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK;;;;;;;8CAExB,8OAAC;oCAAG,WAAU;8CACX,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAChC,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL;;;;;;;;;;;;;;;;sCAYf,8OAAC,OAAO,GAAG;4BAAC,UAAU;;8CACpB,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAiB,WAAU;;;;;;wCAC3B,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK;;;;;;;8CAEtB,8OAAC;oCAAG,WAAU;8CACX,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC9B,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL;;;;;;;;;;;;;;;;;;;;;;8BAajB,8OAAC,OAAO,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,WAAU;;sCAEV,8OAAC,OAAO,GAAG;4BAAC,UAAU;;8CACpB,8OAAC;oCAAG,WAAU;8CAA8B,EAAE,MAAM,CAAC,KAAK;;;;;;8CAC1D,8OAAC;oCAAI,WAAU;8CACZ,EAAE,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBACjC,8OAAC,OAAO,CAAC;4CAEP,MAAM,SAAS,IAAI;4CACnB,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAI;4CACvB,WAAU;sDAEV,cAAA,8OAAC;gDAAK,WAAU;0DAAW,SAAS,IAAI;;;;;;2CANnC;;;;;;;;;;;;;;;;sCAYb,8OAAC,OAAO,GAAG;4BAAC,UAAU;;8CACpB,8OAAC;oCAAG,WAAU;8CAA8B,EAAE,UAAU,CAAC,KAAK;;;;;;8CAC9D,8OAAC;oCAAE,WAAU;8CAAsB,EAAE,UAAU,CAAC,WAAW;;;;;;8CAC3D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,aAAa,EAAE,UAAU,CAAC,WAAW;4CACrC,WAAU;;;;;;sDAEZ,8OAAC,OAAO,MAAM;4CACZ,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;sDAET,EAAE,UAAU,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;8BAM5B,8OAAC,OAAO,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAE,WAAU;sCAA8B,EAAE,SAAS;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAiB,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;AAKhD", "debugId": null}}, {"offset": {"line": 3188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Sharyou/sharyou-landing/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport Header from '@/components/Header';\nimport HeroSection from '@/components/HeroSection';\nimport AIBuilderSection from '@/components/AIBuilderSection';\nimport FeaturesSection from '@/components/FeaturesSection';\nimport TestimonialsSection from '@/components/TestimonialsSection';\nimport Footer from '@/components/Footer';\n\nexport default function Home() {\n  const [language, setLanguage] = useState<'fr' | 'ar'>('fr');\n\n  return (\n    <div className={`min-h-screen ${language === 'ar' ? 'rtl' : 'ltr'}`} dir={language === 'ar' ? 'rtl' : 'ltr'}>\n      <Header language={language} setLanguage={setLanguage} />\n      <main>\n        <HeroSection language={language} />\n        <AIBuilderSection language={language} />\n        <FeaturesSection language={language} />\n        <TestimonialsSection language={language} />\n      </main>\n      <Footer language={language} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEtD,qBACE,8OAAC;QAAI,WAAW,CAAC,aAAa,EAAE,aAAa,OAAO,QAAQ,OAAO;QAAE,KAAK,aAAa,OAAO,QAAQ;;0BACpG,8OAAC,4HAAA,CAAA,UAAM;gBAAC,UAAU;gBAAU,aAAa;;;;;;0BACzC,8OAAC;;kCACC,8OAAC,iIAAA,CAAA,UAAW;wBAAC,UAAU;;;;;;kCACvB,8OAAC,sIAAA,CAAA,UAAgB;wBAAC,UAAU;;;;;;kCAC5B,8OAAC,qIAAA,CAAA,UAAe;wBAAC,UAAU;;;;;;kCAC3B,8OAAC,yIAAA,CAAA,UAAmB;wBAAC,UAAU;;;;;;;;;;;;0BAEjC,8OAAC,4HAAA,CAAA,UAAM;gBAAC,UAAU;;;;;;;;;;;;AAGxB", "debugId": null}}]}