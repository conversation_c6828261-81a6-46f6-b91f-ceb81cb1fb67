'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import { ChevronDownIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';

interface HeaderProps {
  language: 'fr' | 'ar';
  setLanguage: (lang: 'fr' | 'ar') => void;
}

const translations = {
  fr: {
    features: 'Fonctionnalités',
    pricing: 'Tarifs',
    resources: 'Ressources',
    support: 'Support',
    login: 'Connexion',
    signup: 'Commencer gratuitement'
  },
  ar: {
    features: 'الميزات',
    pricing: 'الأسعار',
    resources: 'الموارد',
    support: 'الدعم',
    login: 'تسجيل الدخول',
    signup: 'ابدأ مجاناً'
  }
};

export default function Header({ language, setLanguage }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isLangDropdownOpen, setIsLangDropdownOpen] = useState(false);
  const t = translations[language];

  return (
    <motion.header
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-100"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="flex items-center"
          >
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">S</span>
              </div>
              <span className="text-xl font-bold text-gray-900">Sharyou</span>
            </div>
          </motion.div>

          <nav className="hidden md:flex items-center space-x-8">
            <a href="#features" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
              {t.features}
            </a>
            <a href="#pricing" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
              {t.pricing}
            </a>
            <a href="#resources" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
              {t.resources}
            </a>
            <a href="#support" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
              {t.support}
            </a>
          </nav>

          <div className="hidden md:flex items-center space-x-4">
            <div className="relative">
              <button
                onClick={() => setIsLangDropdownOpen(!isLangDropdownOpen)}
                className="flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors"
              >
                <span className="text-sm font-medium">
                  {language === 'fr' ? 'FR' : 'العربية'}
                </span>
                <ChevronDownIcon className="w-4 h-4" />
              </button>
              
              {isLangDropdownOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full mt-2 right-0 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-[120px]"
                >
                  <button
                    onClick={() => {
                      setLanguage('fr');
                      setIsLangDropdownOpen(false);
                    }}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    Français
                  </button>
                  <button
                    onClick={() => {
                      setLanguage('ar');
                      setIsLangDropdownOpen(false);
                    }}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    العربية
                  </button>
                </motion.div>
              )}
            </div>

            <a href="#login" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
              {t.login}
            </a>
            
            <motion.a
              href="#signup"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary"
            >
              {t.signup}
            </motion.a>
          </div>

          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-blue-600 transition-colors"
            >
              {isMenuOpen ? (
                <XMarkIcon className="w-6 h-6" />
              ) : (
                <Bars3Icon className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden border-t border-gray-100 py-4"
          >
            <div className="flex flex-col space-y-4">
              <a href="#features" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
                {t.features}
              </a>
              <a href="#pricing" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
                {t.pricing}
              </a>
              <a href="#resources" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
                {t.resources}
              </a>
              <a href="#support" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
                {t.support}
              </a>
              <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                <div className="flex space-x-2">
                  <button
                    onClick={() => setLanguage('fr')}
                    className={`px-3 py-1 rounded text-sm ${language === 'fr' ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}`}
                  >
                    FR
                  </button>
                  <button
                    onClick={() => setLanguage('ar')}
                    className={`px-3 py-1 rounded text-sm ${language === 'ar' ? 'bg-blue-100 text-blue-600' : 'text-gray-600'}`}
                  >
                    AR
                  </button>
                </div>
                <div className="flex space-x-2">
                  <a href="#login" className="text-gray-700 hover:text-blue-600 transition-colors font-medium">
                    {t.login}
                  </a>
                  <a href="#signup" className="btn-primary text-sm">
                    {t.signup}
                  </a>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </motion.header>
  );
}
