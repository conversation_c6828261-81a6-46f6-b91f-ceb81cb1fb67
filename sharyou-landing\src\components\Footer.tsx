'use client';

import { motion } from 'framer-motion';
import { 
  EnvelopeIcon, 
  PhoneIcon, 
  MapPinIcon,
  AcademicCapIcon,
  QuestionMarkCircleIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

interface FooterProps {
  language: 'fr' | 'ar';
}

const translations = {
  fr: {
    company: {
      title: 'Sharyou',
      description: 'La plateforme e-commerce algérienne propulsée par l\'IA. Créez votre boutique en ligne en 30 secondes.',
      contact: {
        email: '<EMAIL>',
        phone: '+213 (0) 21 XX XX XX',
        address: 'Alger, Algérie'
      }
    },
    links: {
      resources: {
        title: 'Ressources',
        items: [
          { name: '<PERSON> d\'aide', href: '#help' },
          { name: 'Tu<PERSON>iels vidéo', href: '#tutorials' },
          { name: 'Blog', href: '#blog' },
          { name: 'Webinaires', href: '#webinars' }
        ]
      },
      support: {
        title: 'Support',
        items: [
          { name: 'Support technique', href: '#support' },
          { name: 'FAQ', href: '#faq' },
          { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '#community' },
          { name: 'Status', href: '#status' }
        ]
      },
      legal: {
        title: 'Légal',
        items: [
          { name: 'Conditions d\'utilisation', href: '#terms' },
          { name: 'Politique de confidentialité', href: '#privacy' },
          { name: 'Mentions légales', href: '#legal' },
          { name: 'RGPD', href: '#gdpr' }
        ]
      }
    },
    social: {
      title: 'Suivez-nous',
      platforms: [
        { name: 'Facebook', href: '#facebook', icon: '📘' },
        { name: 'Instagram', href: '#instagram', icon: '📷' },
        { name: 'LinkedIn', href: '#linkedin', icon: '💼' },
        { name: 'YouTube', href: '#youtube', icon: '📺' }
      ]
    },
    newsletter: {
      title: 'Newsletter',
      description: 'Recevez nos dernières actualités et conseils e-commerce',
      placeholder: 'Votre email',
      button: 'S\'abonner'
    },
    copyright: '© 2024 Sharyou. Tous droits réservés.',
    madeIn: 'Fait avec ❤️ en Algérie'
  },
  ar: {
    company: {
      title: 'شاريو',
      description: 'منصة التجارة الإلكترونية الجزائرية المدعومة بالذكاء الاصطناعي. أنشئ متجرك الإلكتروني في 30 ثانية.',
      contact: {
        email: '<EMAIL>',
        phone: '+213 (0) 21 XX XX XX',
        address: 'الجزائر العاصمة، الجزائر'
      }
    },
    links: {
      resources: {
        title: 'الموارد',
        items: [
          { name: 'مركز المساعدة', href: '#help' },
          { name: 'دروس فيديو', href: '#tutorials' },
          { name: 'المدونة', href: '#blog' },
          { name: 'ندوات ويب', href: '#webinars' }
        ]
      },
      support: {
        title: 'الدعم',
        items: [
          { name: 'الدعم التقني', href: '#support' },
          { name: 'الأسئلة الشائعة', href: '#faq' },
          { name: 'المجتمع', href: '#community' },
          { name: 'الحالة', href: '#status' }
        ]
      },
      legal: {
        title: 'قانوني',
        items: [
          { name: 'شروط الاستخدام', href: '#terms' },
          { name: 'سياسة الخصوصية', href: '#privacy' },
          { name: 'الإشعارات القانونية', href: '#legal' },
          { name: 'حماية البيانات', href: '#gdpr' }
        ]
      }
    },
    social: {
      title: 'تابعنا',
      platforms: [
        { name: 'فيسبوك', href: '#facebook', icon: '📘' },
        { name: 'إنستغرام', href: '#instagram', icon: '📷' },
        { name: 'لينكد إن', href: '#linkedin', icon: '💼' },
        { name: 'يوتيوب', href: '#youtube', icon: '📺' }
      ]
    },
    newsletter: {
      title: 'النشرة الإخبارية',
      description: 'احصل على آخر أخبارنا ونصائح التجارة الإلكترونية',
      placeholder: 'بريدك الإلكتروني',
      button: 'اشتراك'
    },
    copyright: '© 2024 شاريو. جميع الحقوق محفوظة.',
    madeIn: 'صُنع بـ ❤️ في الجزائر'
  }
};

export default function Footer({ language }: FooterProps) {
  const t = translations[language];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12"
        >
          <motion.div variants={itemVariants} className="lg:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">S</span>
              </div>
              <span className="text-xl font-bold">{t.company.title}</span>
            </div>
            <p className="text-gray-400 mb-6 leading-relaxed">
              {t.company.description}
            </p>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <EnvelopeIcon className="w-5 h-5 text-blue-400" />
                <span className="text-gray-400">{t.company.contact.email}</span>
              </div>
              <div className="flex items-center space-x-3">
                <PhoneIcon className="w-5 h-5 text-blue-400" />
                <span className="text-gray-400">{t.company.contact.phone}</span>
              </div>
              <div className="flex items-center space-x-3">
                <MapPinIcon className="w-5 h-5 text-blue-400" />
                <span className="text-gray-400">{t.company.contact.address}</span>
              </div>
            </div>
          </motion.div>

          <motion.div variants={itemVariants}>
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <AcademicCapIcon className="w-5 h-5 mr-2 text-blue-400" />
              {t.links.resources.title}
            </h3>
            <ul className="space-y-3">
              {t.links.resources.items.map((item, index) => (
                <li key={index}>
                  <a 
                    href={item.href}
                    className="text-gray-400 hover:text-white transition-colors duration-200"
                  >
                    {item.name}
                  </a>
                </li>
              ))}
            </ul>
          </motion.div>

          <motion.div variants={itemVariants}>
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <QuestionMarkCircleIcon className="w-5 h-5 mr-2 text-blue-400" />
              {t.links.support.title}
            </h3>
            <ul className="space-y-3">
              {t.links.support.items.map((item, index) => (
                <li key={index}>
                  <a 
                    href={item.href}
                    className="text-gray-400 hover:text-white transition-colors duration-200"
                  >
                    {item.name}
                  </a>
                </li>
              ))}
            </ul>
          </motion.div>

          <motion.div variants={itemVariants}>
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <DocumentTextIcon className="w-5 h-5 mr-2 text-blue-400" />
              {t.links.legal.title}
            </h3>
            <ul className="space-y-3">
              {t.links.legal.items.map((item, index) => (
                <li key={index}>
                  <a 
                    href={item.href}
                    className="text-gray-400 hover:text-white transition-colors duration-200"
                  >
                    {item.name}
                  </a>
                </li>
              ))}
            </ul>
          </motion.div>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12"
        >
          <motion.div variants={itemVariants}>
            <h3 className="text-lg font-semibold mb-4">{t.social.title}</h3>
            <div className="flex space-x-4">
              {t.social.platforms.map((platform, index) => (
                <motion.a
                  key={index}
                  href={platform.href}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-gray-700 transition-colors duration-200"
                >
                  <span className="text-xl">{platform.icon}</span>
                </motion.a>
              ))}
            </div>
          </motion.div>

          <motion.div variants={itemVariants}>
            <h3 className="text-lg font-semibold mb-4">{t.newsletter.title}</h3>
            <p className="text-gray-400 mb-4">{t.newsletter.description}</p>
            <div className="flex">
              <input
                type="email"
                placeholder={t.newsletter.placeholder}
                className="flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-l-lg focus:outline-none focus:border-blue-500 text-white"
              />
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-r-lg font-semibold transition-colors duration-200"
              >
                {t.newsletter.button}
              </motion.button>
            </div>
          </motion.div>
        </motion.div>

        <motion.div
          variants={itemVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center"
        >
          <p className="text-gray-400 mb-4 md:mb-0">{t.copyright}</p>
          <p className="text-gray-400">{t.madeIn}</p>
        </motion.div>
      </div>
    </footer>
  );
}
