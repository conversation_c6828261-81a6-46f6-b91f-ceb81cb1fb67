'use client';

import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import { StarIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/solid';

interface TestimonialsSectionProps {
  language: 'fr' | 'ar';
}

const translations = {
  fr: {
    title: 'Ils ont réussi avec Sharyou',
    subtitle: 'Découvrez les success stories d\'entrepreneurs algériens',
    testimonials: [
      {
        name: '<PERSON><PERSON>',
        role: 'Fondatrice, Bijoux Berbères',
        location: '<PERSON>ger',
        image: '/testimonials/amina.jpg',
        content: 'Sharyou a transformé ma passion pour les bijoux traditionnels en véritable business. En 30 secondes, j\'avais ma boutique en ligne. Aujourd\'hui, je vends dans toute l\'Algérie !',
        revenue: '+300% de ventes',
        timeframe: 'en 3 mois'
      },
      {
        name: '<PERSON><PERSON>',
        role: 'CEO, TechStore DZ',
        location: '<PERSON><PERSON>',
        image: '/testimonials/karim.jpg',
        content: 'L\'IA de Sharyou a créé exactement le design que j\'imaginais pour ma boutique tech. Le support multilingue m\'a permis de toucher tous mes clients.',
        revenue: '50 000 DA/jour',
        timeframe: 'de chiffre d\'affaires'
      },
      {
        name: 'Fatima Zohra',
        role: 'Artisane, Poterie Kabyle',
        location: 'Tizi Ouzou',
        image: '/testimonials/fatima.jpg',
        content: 'Je ne connaissais rien au web. Avec Sharyou, j\'ai pu créer ma boutique et vendre mes poteries dans le monde entier. C\'est magique !',
        revenue: 'Export international',
        timeframe: 'dès le 1er mois'
      }
    ],
    metrics: {
      satisfaction: '98%',
      satisfactionLabel: 'Satisfaction client',
      growth: '+250%',
      growthLabel: 'Croissance moyenne',
      support: '24/7',
      supportLabel: 'Support technique'
    }
  },
  ar: {
    title: 'نجحوا مع شاريو',
    subtitle: 'اكتشف قصص نجاح رجال الأعمال الجزائريين',
    testimonials: [
      {
        name: 'أمينة بن علي',
        role: 'مؤسسة، مجوهرات أمازيغية',
        location: 'الجزائر العاصمة',
        image: '/testimonials/amina.jpg',
        content: 'شاريو حوّل شغفي بالمجوهرات التقليدية إلى عمل حقيقي. في 30 ثانية، كان لدي متجري الإلكتروني. اليوم، أبيع في كل الجزائر!',
        revenue: '+300% في المبيعات',
        timeframe: 'في 3 أشهر'
      },
      {
        name: 'كريم مسعودي',
        role: 'المدير التنفيذي، تك ستور دي زد',
        location: 'وهران',
        image: '/testimonials/karim.jpg',
        content: 'ذكاء شاريو الاصطناعي أنشأ بالضبط التصميم الذي تخيلته لمتجر التكنولوجيا. الدعم متعدد اللغات مكنني من الوصول لجميع عملائي.',
        revenue: '50,000 دج/يوم',
        timeframe: 'من رقم الأعمال'
      },
      {
        name: 'فاطمة الزهراء',
        role: 'حرفية، فخار قبائلي',
        location: 'تيزي وزو',
        image: '/testimonials/fatima.jpg',
        content: 'لم أكن أعرف شيئاً عن الويب. مع شاريو، تمكنت من إنشاء متجري وبيع فخارياتي في العالم كله. إنه سحر!',
        revenue: 'تصدير دولي',
        timeframe: 'من الشهر الأول'
      }
    ],
    metrics: {
      satisfaction: '98%',
      satisfactionLabel: 'رضا العملاء',
      growth: '+250%',
      growthLabel: 'النمو المتوسط',
      support: '24/7',
      supportLabel: 'الدعم التقني'
    }
  }
};

export default function TestimonialsSection({ language }: TestimonialsSectionProps) {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const t = translations[language];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % t.testimonials.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [t.testimonials.length]);

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % t.testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + t.testimonials.length) % t.testimonials.length);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="testimonials" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-3xl md:text-5xl font-bold text-gray-900 mb-6"
          >
            {t.title}
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-3xl mx-auto"
          >
            {t.subtitle}
          </motion.p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            className="relative"
          >
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl p-8 relative overflow-hidden">
              <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
              
              <motion.div
                key={currentTestimonial}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.5 }}
                className="relative z-10"
              >
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                    {t.testimonials[currentTestimonial].name.charAt(0)}
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">
                      {t.testimonials[currentTestimonial].name}
                    </h3>
                    <p className="text-gray-600">
                      {t.testimonials[currentTestimonial].role}
                    </p>
                    <p className="text-sm text-gray-500">
                      {t.testimonials[currentTestimonial].location}
                    </p>
                  </div>
                </div>

                <div className="flex mb-4">
                  {[...Array(5)].map((_, i) => (
                    <StarIcon key={i} className="w-5 h-5 text-yellow-400" />
                  ))}
                </div>

                <blockquote className="text-lg text-gray-700 mb-6 leading-relaxed">
                  "{t.testimonials[currentTestimonial].content}"
                </blockquote>

                <div className="flex items-center justify-between bg-white rounded-xl p-4">
                  <div>
                    <div className="text-2xl font-bold text-green-600">
                      {t.testimonials[currentTestimonial].revenue}
                    </div>
                    <div className="text-sm text-gray-600">
                      {t.testimonials[currentTestimonial].timeframe}
                    </div>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-2xl">📈</span>
                  </div>
                </div>
              </motion.div>

              <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-200 rounded-full opacity-20"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-purple-200 rounded-full opacity-20"></div>
            </div>

            <div className="flex justify-center items-center mt-6 space-x-4">
              <button
                onClick={prevTestimonial}
                className="w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors"
              >
                <ChevronLeftIcon className="w-5 h-5 text-gray-600" />
              </button>

              <div className="flex space-x-2">
                {t.testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentTestimonial(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === currentTestimonial ? 'bg-blue-600' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>

              <button
                onClick={nextTestimonial}
                className="w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors"
              >
                <ChevronRightIcon className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            className="space-y-8"
          >
            <motion.div variants={itemVariants} className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">{t.metrics.satisfaction}</div>
              <div className="text-gray-600">{t.metrics.satisfactionLabel}</div>
            </motion.div>

            <motion.div variants={itemVariants} className="text-center">
              <div className="text-4xl font-bold text-green-600 mb-2">{t.metrics.growth}</div>
              <div className="text-gray-600">{t.metrics.growthLabel}</div>
            </motion.div>

            <motion.div variants={itemVariants} className="text-center">
              <div className="text-4xl font-bold text-purple-600 mb-2">{t.metrics.support}</div>
              <div className="text-gray-600">{t.metrics.supportLabel}</div>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl p-6 text-white text-center"
            >
              <h3 className="text-xl font-bold mb-2">
                {language === 'fr' ? 'Rejoignez-les !' : 'انضم إليهم!'}
              </h3>
              <p className="mb-4">
                {language === 'fr' 
                  ? 'Plus de 10,000 entrepreneurs nous font confiance'
                  : 'أكثر من 10,000 رجل أعمال يثقون بنا'
                }
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white text-blue-600 font-semibold py-3 px-6 rounded-lg hover:bg-gray-50 transition-colors"
              >
                {language === 'fr' ? 'Commencer gratuitement' : 'ابدأ مجاناً'}
              </motion.button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
