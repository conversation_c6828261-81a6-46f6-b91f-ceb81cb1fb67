'use client';

import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import { 
  ChatBubbleLeftRightIcon, 
  PaintBrushIcon, 
  CogIcon, 
  RocketLaunchIcon,
  CheckIcon 
} from '@heroicons/react/24/outline';

interface AIBuilderSectionProps {
  language: 'fr' | 'ar';
}

const translations = {
  fr: {
    title: 'Comment l\'IA crée votre boutique',
    subtitle: 'Un processus révolutionnaire en 4 étapes simples',
    steps: [
      {
        icon: ChatBubbleLeftRightIcon,
        title: 'Décrivez votre vision',
        description: 'Parlez à notre IA de votre produit, votre marque et vos objectifs en quelques mots.',
        time: '10 sec'
      },
      {
        icon: PaintBrushIcon,
        title: 'IA génère le design',
        description: 'Notre intelligence artificielle crée un design unique adapté à votre secteur.',
        time: '15 sec'
      },
      {
        icon: CogIcon,
        title: 'Configuration automatique',
        description: 'Paie<PERSON>, livraison, SEO - tout est configuré automatiquement pour l\'Algérie.',
        time: '5 sec'
      },
      {
        icon: RocketLaunchIcon,
        title: 'Boutique prête',
        description: 'Votre boutique est en ligne, optimisée et prête à recevoir vos premiers clients.',
        time: '0 sec'
      }
    ],
    stats: {
      speed: '10x plus rapide',
      speedDesc: 'qu\'une création manuelle',
      accuracy: '99% de précision',
      accuracyDesc: 'dans la génération',
      optimization: 'SEO optimisé',
      optimizationDesc: 'automatiquement'
    },
    cta: 'Essayer l\'IA Builder'
  },
  ar: {
    title: 'كيف ينشئ الذكاء الاصطناعي متجرك',
    subtitle: 'عملية ثورية في 4 خطوات بسيطة',
    steps: [
      {
        icon: ChatBubbleLeftRightIcon,
        title: 'صف رؤيتك',
        description: 'أخبر ذكاءنا الاصطناعي عن منتجك وعلامتك التجارية وأهدافك في كلمات قليلة.',
        time: '10 ثواني'
      },
      {
        icon: PaintBrushIcon,
        title: 'الذكاء الاصطناعي ينشئ التصميم',
        description: 'ذكاؤنا الاصطناعي ينشئ تصميماً فريداً مناسباً لقطاعك.',
        time: '15 ثانية'
      },
      {
        icon: CogIcon,
        title: 'التكوين التلقائي',
        description: 'المدفوعات، التوصيل، السيو - كل شيء مُكوّن تلقائياً للجزائر.',
        time: '5 ثواني'
      },
      {
        icon: RocketLaunchIcon,
        title: 'المتجر جاهز',
        description: 'متجرك متاح على الإنترنت، محسّن وجاهز لاستقبال عملائك الأوائل.',
        time: '0 ثانية'
      }
    ],
    stats: {
      speed: 'أسرع 10 مرات',
      speedDesc: 'من الإنشاء اليدوي',
      accuracy: '99% دقة',
      accuracyDesc: 'في التوليد',
      optimization: 'محسّن للسيو',
      optimizationDesc: 'تلقائياً'
    },
    cta: 'جرب منشئ الذكاء الاصطناعي'
  }
};

export default function AIBuilderSection({ language }: AIBuilderSectionProps) {
  const [activeStep, setActiveStep] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const t = translations[language];

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % t.steps.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [t.steps.length]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="ai-builder" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-3xl md:text-5xl font-bold text-gray-900 mb-6"
          >
            {t.title}
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-3xl mx-auto"
          >
            {t.subtitle}
          </motion.p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            className="space-y-8"
          >
            {t.steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = index === activeStep;
              const isCompleted = index < activeStep;

              return (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className={`relative flex items-start space-x-4 p-6 rounded-2xl transition-all duration-500 ${
                    isActive 
                      ? 'bg-blue-50 border-2 border-blue-200 shadow-lg' 
                      : isCompleted 
                        ? 'bg-green-50 border-2 border-green-200' 
                        : 'bg-gray-50 border-2 border-transparent'
                  }`}
                >
                  <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center transition-all duration-500 ${
                    isActive 
                      ? 'bg-blue-600 text-white' 
                      : isCompleted 
                        ? 'bg-green-600 text-white' 
                        : 'bg-gray-300 text-gray-600'
                  }`}>
                    {isCompleted ? (
                      <CheckIcon className="w-6 h-6" />
                    ) : (
                      <Icon className="w-6 h-6" />
                    )}
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className={`text-lg font-semibold transition-colors duration-500 ${
                        isActive ? 'text-blue-900' : isCompleted ? 'text-green-900' : 'text-gray-700'
                      }`}>
                        {step.title}
                      </h3>
                      <span className={`text-sm font-medium px-2 py-1 rounded-full transition-all duration-500 ${
                        isActive 
                          ? 'bg-blue-100 text-blue-700' 
                          : isCompleted 
                            ? 'bg-green-100 text-green-700' 
                            : 'bg-gray-200 text-gray-600'
                      }`}>
                        {step.time}
                      </span>
                    </div>
                    <p className={`transition-colors duration-500 ${
                      isActive ? 'text-blue-700' : isCompleted ? 'text-green-700' : 'text-gray-600'
                    }`}>
                      {step.description}
                    </p>
                  </div>

                  {isActive && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute -left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-600 rounded-full"
                    />
                  )}
                </motion.div>
              );
            })}
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            className="relative"
          >
            <div className="bg-gradient-to-br from-blue-600 to-purple-600 rounded-3xl p-8 text-white relative overflow-hidden">
              <div className="absolute inset-0 bg-grid-pattern opacity-10"></div>
              
              <motion.div variants={itemVariants} className="relative z-10">
                <h3 className="text-2xl font-bold mb-8">Statistiques IA</h3>
                
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-3xl font-bold">{t.stats.speed}</div>
                      <div className="text-blue-100">{t.stats.speedDesc}</div>
                    </div>
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                      <RocketLaunchIcon className="w-8 h-8" />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-3xl font-bold">{t.stats.accuracy}</div>
                      <div className="text-blue-100">{t.stats.accuracyDesc}</div>
                    </div>
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                      <CheckIcon className="w-8 h-8" />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-3xl font-bold">{t.stats.optimization}</div>
                      <div className="text-blue-100">{t.stats.optimizationDesc}</div>
                    </div>
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                      <CogIcon className="w-8 h-8" />
                    </div>
                  </div>
                </div>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full mt-8 bg-white text-blue-600 font-semibold py-4 px-6 rounded-xl hover:bg-gray-50 transition-colors"
                >
                  {t.cta}
                </motion.button>
              </motion.div>

              <div className="absolute -top-8 -right-8 w-32 h-32 bg-white/10 rounded-full"></div>
              <div className="absolute -bottom-8 -left-8 w-24 h-24 bg-white/10 rounded-full"></div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
