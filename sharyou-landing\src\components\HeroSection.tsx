'use client';

import { motion } from 'framer-motion';
import { SparklesIcon, ArrowRightIcon, PlayIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';

interface HeroSectionProps {
  language: 'fr' | 'ar';
}

const translations = {
  fr: {
    badge: 'Propulsé par l\'IA',
    title: 'Créez votre boutique en ligne en',
    titleHighlight: '30 secondes',
    titleEnd: 'avec l\'IA',
    subtitle: 'Sharyou révolutionne l\'e-commerce en Algérie. Notre IA génère automatiquement votre boutique complète, optimisée et prête à vendre.',
    cta1: 'Créer ma boutique IA',
    cta2: 'Voir la démo',
    stats: {
      stores: '10,000+',
      storesLabel: 'Boutiques créées',
      time: '30 sec',
      timeLabel: 'Temps moyen',
      success: '95%',
      successLabel: 'Taux de réussite'
    }
  },
  ar: {
    badge: 'مدعوم بالذكاء الاصطناعي',
    title: 'أنشئ متجرك الإلكتروني في',
    titleHighlight: '30 ثانية',
    titleEnd: 'بالذكاء الاصطناعي',
    subtitle: 'شاريو يثور التجارة الإلكترونية في الجزائر. ذكاؤنا الاصطناعي ينشئ متجرك الكامل تلقائياً، محسّن وجاهز للبيع.',
    cta1: 'إنشاء متجري بالذكاء الاصطناعي',
    cta2: 'مشاهدة العرض التوضيحي',
    stats: {
      stores: '10,000+',
      storesLabel: 'متجر تم إنشاؤه',
      time: '30 ثانية',
      timeLabel: 'متوسط الوقت',
      success: '95%',
      successLabel: 'معدل النجاح'
    }
  }
};

export default function HeroSection({ language }: HeroSectionProps) {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const t = translations[language];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden gradient-hero pt-16">
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>

      {/* Floating elements for visual appeal */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-float"></div>
      <div className="absolute top-40 right-20 w-16 h-16 bg-purple-200 rounded-full opacity-30 animate-float" style={{ animationDelay: '1s' }}></div>
      <div className="absolute bottom-40 left-20 w-12 h-12 bg-yellow-200 rounded-full opacity-25 animate-float" style={{ animationDelay: '2s' }}></div>
      
      <div className="relative z-10 max-w-7xl mx-auto container-padding section-padding">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="text-center"
        >
          <motion.div variants={itemVariants} className="mb-8">
            <span className="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 text-sm font-medium">
              <SparklesIcon className="w-4 h-4 mr-2" />
              {t.badge}
            </span>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="heading-xl text-gray-900 mb-6"
          >
            {t.title}{' '}
            <span className="text-gradient">
              {t.titleHighlight}
            </span>
            <br />
            {t.titleEnd}
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed"
          >
            {t.subtitle}
          </motion.p>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
          >
            <motion.button
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
              className="btn-primary text-lg px-8 py-4 flex items-center group will-change-transform"
            >
              {t.cta1}
              <ArrowRightIcon className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.02, y: -1 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
              onClick={() => setIsVideoPlaying(true)}
              className="btn-secondary text-lg px-8 py-4 flex items-center group will-change-transform"
            >
              <PlayIcon className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
              {t.cta2}
            </motion.button>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto mb-16"
          >
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">{t.stats.stores}</div>
              <div className="text-gray-600">{t.stats.storesLabel}</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">{t.stats.time}</div>
              <div className="text-gray-600">{t.stats.timeLabel}</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">{t.stats.success}</div>
              <div className="text-gray-600">{t.stats.successLabel}</div>
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="relative max-w-4xl mx-auto"
          >
            <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-white p-2 card">
              <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl flex items-center justify-center relative overflow-hidden">
                {!isVideoPlaying ? (
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    transition={{ type: "spring", stiffness: 400, damping: 17 }}
                    onClick={() => setIsVideoPlaying(true)}
                    className="w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 will-change-transform animate-pulse-glow"
                  >
                    <PlayIcon className="w-8 h-8 text-blue-600 ml-1" />
                  </motion.button>
                ) : (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5 }}
                    className="w-full h-full bg-gray-900 flex items-center justify-center"
                  >
                    <div className="text-white text-center">
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.2, type: "spring" }}
                        className="text-4xl mb-4"
                      >
                        🎬
                      </motion.div>
                      <motion.p
                        initial={{ y: 20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.4 }}
                        className="text-xl mb-2"
                      >
                        Vidéo de démonstration
                      </motion.p>
                      <motion.p
                        initial={{ y: 20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.6 }}
                        className="text-sm text-gray-400"
                      >
                        Découvrez comment l'IA crée votre boutique
                      </motion.p>
                    </div>
                  </motion.div>
                )}

                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent pointer-events-none"></div>
              </div>
            </div>

            <div className="absolute -top-4 -left-4 w-24 h-24 bg-yellow-400 rounded-full opacity-20 animate-pulse-glow"></div>
            <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-blue-400 rounded-full opacity-20 animate-pulse-glow" style={{ animationDelay: '1s' }}></div>
          </motion.div>
        </motion.div>
      </div>

      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center"
        >
          <div className="w-1 h-3 bg-gray-400 rounded-full mt-2"></div>
        </motion.div>
      </div>
    </section>
  );
}
