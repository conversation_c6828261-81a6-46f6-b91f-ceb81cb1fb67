'use client';

import { motion } from 'framer-motion';
import { SparklesIcon, ArrowRightIcon, PlayIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';

interface HeroSectionProps {
  language: 'fr' | 'ar';
}

const translations = {
  fr: {
    badge: 'Propulsé par l\'IA',
    title: 'Créez votre boutique en ligne en',
    titleHighlight: '30 secondes',
    titleEnd: 'avec l\'IA',
    subtitle: 'Sharyou révolutionne l\'e-commerce en Algérie. Notre IA génère automatiquement votre boutique complète, optimisée et prête à vendre.',
    cta1: 'Créer ma boutique IA',
    cta2: 'Voir la démo',
    stats: {
      stores: '10,000+',
      storesLabel: 'Boutiques créées',
      time: '30 sec',
      timeLabel: 'Temps moyen',
      success: '95%',
      successLabel: 'Taux de réussite'
    }
  },
  ar: {
    badge: 'مدعوم بالذكاء الاصطناعي',
    title: 'أنشئ متجرك الإلكتروني في',
    titleHighlight: '30 ثانية',
    titleEnd: 'بالذكاء الاصطناعي',
    subtitle: 'شاريو يثور التجارة الإلكترونية في الجزائر. ذكاؤنا الاصطناعي ينشئ متجرك الكامل تلقائياً، محسّن وجاهز للبيع.',
    cta1: 'إنشاء متجري بالذكاء الاصطناعي',
    cta2: 'مشاهدة العرض التوضيحي',
    stats: {
      stores: '10,000+',
      storesLabel: 'متجر تم إنشاؤه',
      time: '30 ثانية',
      timeLabel: 'متوسط الوقت',
      success: '95%',
      successLabel: 'معدل النجاح'
    }
  }
};

export default function HeroSection({ language }: HeroSectionProps) {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const t = translations[language];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 pt-16">
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="text-center"
        >
          <motion.div variants={itemVariants} className="mb-8">
            <span className="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 text-sm font-medium">
              <SparklesIcon className="w-4 h-4 mr-2" />
              {t.badge}
            </span>
          </motion.div>

          <motion.h1
            variants={itemVariants}
            className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 mb-6 leading-tight"
          >
            {t.title}{' '}
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {t.titleHighlight}
            </span>
            <br />
            {t.titleEnd}
          </motion.h1>

          <motion.p
            variants={itemVariants}
            className="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed"
          >
            {t.subtitle}
          </motion.p>

          <motion.div
            variants={itemVariants}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
          >
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary text-lg px-8 py-4 flex items-center group"
            >
              {t.cta1}
              <ArrowRightIcon className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setIsVideoPlaying(true)}
              className="btn-secondary text-lg px-8 py-4 flex items-center group"
            >
              <PlayIcon className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
              {t.cta2}
            </motion.button>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto mb-16"
          >
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">{t.stats.stores}</div>
              <div className="text-gray-600">{t.stats.storesLabel}</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">{t.stats.time}</div>
              <div className="text-gray-600">{t.stats.timeLabel}</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">{t.stats.success}</div>
              <div className="text-gray-600">{t.stats.successLabel}</div>
            </div>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="relative max-w-4xl mx-auto"
          >
            <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-white p-2">
              <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl flex items-center justify-center relative overflow-hidden">
                {!isVideoPlaying ? (
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setIsVideoPlaying(true)}
                    className="w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-shadow"
                  >
                    <PlayIcon className="w-8 h-8 text-blue-600 ml-1" />
                  </motion.button>
                ) : (
                  <div className="w-full h-full bg-gray-900 flex items-center justify-center">
                    <div className="text-white text-center">
                      <div className="text-2xl mb-4">🎬</div>
                      <p>Vidéo de démonstration</p>
                      <p className="text-sm text-gray-400 mt-2">
                        Intégration vidéo à venir
                      </p>
                    </div>
                  </div>
                )}
                
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent pointer-events-none"></div>
              </div>
            </div>
            
            <div className="absolute -top-4 -left-4 w-24 h-24 bg-yellow-400 rounded-full opacity-20 animate-pulse"></div>
            <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-blue-400 rounded-full opacity-20 animate-pulse delay-1000"></div>
          </motion.div>
        </motion.div>
      </div>

      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center"
        >
          <div className="w-1 h-3 bg-gray-400 rounded-full mt-2"></div>
        </motion.div>
      </div>
    </section>
  );
}
