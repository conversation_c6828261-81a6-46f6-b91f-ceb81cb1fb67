@import "tailwindcss";

:root {
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-900: #1e3a8a;

  --secondary-400: #facc15;
  --secondary-500: #eab308;
  --secondary-600: #ca8a04;

  --accent-500: #10b981;
  --accent-600: #059669;

  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  --background: #ffffff;
  --surface: #f9fafb;

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 5rem;
  font-size: 16px;
}

@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

body {
  font-family: var(--font-inter), 'Inter', system-ui, -apple-system, sans-serif;
  color: var(--gray-800);
  background: var(--background);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
}

/* RTL Support */
[dir="rtl"] {
  font-family: 'Noto Sans Arabic', system-ui, sans-serif;
}

[dir="rtl"] * {
  text-align: right;
}

[dir="rtl"] .text-left {
  text-align: right !important;
}

[dir="rtl"] .text-right {
  text-align: left !important;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Improved focus styles */
*:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Optimized Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 2rem, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translate3d(-2rem, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translate3d(2rem, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.animate-slide-in-up {
  animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.animate-scale-in {
  animation: scaleIn 0.5s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-500) 0%, var(--secondary-600) 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, var(--accent-500) 0%, var(--accent-600) 100%);
}

.gradient-hero {
  background: linear-gradient(135deg, var(--primary-50) 0%, #ffffff 50%, var(--primary-50) 100%);
}

/* Optimized Button styles */
.btn-primary {
  @apply inline-flex items-center justify-center px-4 py-2 sm:px-6 sm:py-3 text-sm sm:text-base font-semibold text-white transition-all duration-300 ease-out transform;
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  min-height: 44px; /* Touch target size */
}

.btn-primary:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.btn-primary:active {
  transform: translateY(0) scale(0.98);
}

.btn-primary:focus {
  outline: 2px solid var(--primary-300);
  outline-offset: 2px;
}

.btn-primary:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover:before {
  left: 100%;
}

.btn-secondary {
  @apply inline-flex items-center justify-center px-4 py-2 sm:px-6 sm:py-3 text-sm sm:text-base font-semibold transition-all duration-300 ease-out;
  background: transparent;
  color: var(--primary-600);
  border: 2px solid var(--primary-600);
  border-radius: var(--radius-lg);
  cursor: pointer;
  min-height: 44px; /* Touch target size */
}

.btn-secondary:hover {
  background: var(--primary-600);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary:focus {
  outline: 2px solid var(--primary-300);
  outline-offset: 2px;
}

/* Button sizes */
.btn-sm {
  @apply px-3 py-1.5 text-sm;
  min-height: 36px;
}

.btn-lg {
  @apply px-8 py-4 text-lg;
  min-height: 52px;
}

.btn-xl {
  @apply px-10 py-5 text-xl;
  min-height: 60px;
}

/* Grid pattern */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Card styles */
.card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.card-glass {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Section spacing */
.section-padding {
  @apply py-12 md:py-16 lg:py-20 xl:py-24;
}

.section-padding-sm {
  @apply py-8 md:py-12 lg:py-16;
}

.section-padding-lg {
  @apply py-20 md:py-24 lg:py-28 xl:py-32;
}

.container-padding {
  @apply px-4 sm:px-6 lg:px-8 xl:px-12;
}

.container-max-width {
  @apply max-w-7xl mx-auto;
}

/* Content spacing */
.content-spacing {
  @apply space-y-6 md:space-y-8 lg:space-y-10;
}

.content-spacing-sm {
  @apply space-y-4 md:space-y-6;
}

.content-spacing-lg {
  @apply space-y-8 md:space-y-12 lg:space-y-16;
}

/* Typography improvements */
.heading-xl {
  @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.heading-lg {
  @apply text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold;
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.heading-md {
  @apply text-xl sm:text-2xl md:text-3xl font-bold;
  line-height: 1.3;
}

.heading-sm {
  @apply text-lg sm:text-xl md:text-2xl font-semibold;
  line-height: 1.4;
}

.text-body-lg {
  @apply text-lg md:text-xl;
  line-height: 1.6;
}

.text-body {
  @apply text-base md:text-lg;
  line-height: 1.6;
}

.text-body-sm {
  @apply text-sm md:text-base;
  line-height: 1.5;
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

/* Text overflow handling */
.text-ellipsis {
  @apply truncate;
}

.text-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}



/* Responsive utilities */
@media (max-width: 640px) {
  .heading-xl {
    @apply text-3xl;
  }

  .heading-lg {
    @apply text-2xl;
  }

  .btn-primary,
  .btn-secondary {
    @apply px-4 py-2 text-sm;
  }

  .section-padding {
    @apply py-12;
  }
}

@media (max-width: 768px) {
  .container-padding {
    @apply px-4;
  }
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
}


